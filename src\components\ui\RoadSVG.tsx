/**
 * Road-themed SVG Component
 * Replaces any love/heart SVGs with road-themed alternatives
 */

import React from 'react';

interface RoadSVGProps {
  className?: string;
  size?: number;
  variant?: 'road' | 'highway' | 'intersection' | 'bridge' | 'tunnel';
  color?: string;
}

const RoadSVG: React.FC<RoadSVGProps> = ({ 
  className = '', 
  size = 24, 
  variant = 'road',
  color = '#f97316' // Default orange color
}) => {
  const renderRoadVariant = () => {
    switch (variant) {
      case 'road':
        return (
          <svg
            width={size}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={className}
          >
            {/* Road surface */}
            <rect x="6" y="4" width="12" height="16" fill={color} rx="1" />
            
            {/* Road markings - center line */}
            <rect x="11.5" y="5" width="1" height="2" fill="white" />
            <rect x="11.5" y="8" width="1" height="2" fill="white" />
            <rect x="11.5" y="11" width="1" height="2" fill="white" />
            <rect x="11.5" y="14" width="1" height="2" fill="white" />
            <rect x="11.5" y="17" width="1" height="2" fill="white" />
            
            {/* Side lines */}
            <rect x="6.5" y="4" width="0.5" height="16" fill="white" />
            <rect x="17" y="4" width="0.5" height="16" fill="white" />
            
            {/* Road edges */}
            <rect x="5" y="4" width="1" height="16" fill="#6b7280" />
            <rect x="18" y="4" width="1" height="16" fill="#6b7280" />
          </svg>
        );

      case 'highway':
        return (
          <svg
            width={size}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={className}
          >
            {/* Highway surface */}
            <rect x="4" y="6" width="16" height="12" fill={color} rx="1" />
            
            {/* Multiple lanes */}
            <rect x="4.5" y="6" width="0.5" height="12" fill="white" />
            <rect x="8" y="7" width="0.5" height="2" fill="white" />
            <rect x="8" y="10" width="0.5" height="2" fill="white" />
            <rect x="8" y="13" width="0.5" height="2" fill="white" />
            <rect x="8" y="16" width="0.5" height="1" fill="white" />
            
            <rect x="11.75" y="7" width="0.5" height="2" fill="white" />
            <rect x="11.75" y="10" width="0.5" height="2" fill="white" />
            <rect x="11.75" y="13" width="0.5" height="2" fill="white" />
            <rect x="11.75" y="16" width="0.5" height="1" fill="white" />
            
            <rect x="15.5" y="7" width="0.5" height="2" fill="white" />
            <rect x="15.5" y="10" width="0.5" height="2" fill="white" />
            <rect x="15.5" y="13" width="0.5" height="2" fill="white" />
            <rect x="15.5" y="16" width="0.5" height="1" fill="white" />
            
            <rect x="19" y="6" width="0.5" height="12" fill="white" />
            
            {/* Highway barriers */}
            <rect x="3" y="6" width="0.5" height="12" fill="#6b7280" />
            <rect x="20.5" y="6" width="0.5" height="12" fill="#6b7280" />
          </svg>
        );

      case 'intersection':
        return (
          <svg
            width={size}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={className}
          >
            {/* Horizontal road */}
            <rect x="2" y="10" width="20" height="4" fill={color} />
            
            {/* Vertical road */}
            <rect x="10" y="2" width="4" height="20" fill={color} />
            
            {/* Intersection markings */}
            <rect x="10.5" y="3" width="0.5" height="2" fill="white" />
            <rect x="13" y="3" width="0.5" height="2" fill="white" />
            <rect x="10.5" y="19" width="0.5" height="2" fill="white" />
            <rect x="13" y="19" width="0.5" height="2" fill="white" />
            
            <rect x="3" y="10.5" width="2" height="0.5" fill="white" />
            <rect x="3" y="13" width="2" height="0.5" fill="white" />
            <rect x="19" y="10.5" width="2" height="0.5" fill="white" />
            <rect x="19" y="13" width="2" height="0.5" fill="white" />
            
            {/* Traffic light */}
            <circle cx="12" cy="12" r="1.5" fill="#ef4444" />
          </svg>
        );

      case 'bridge':
        return (
          <svg
            width={size}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={className}
          >
            {/* Bridge deck */}
            <rect x="4" y="10" width="16" height="4" fill={color} rx="0.5" />
            
            {/* Bridge supports */}
            <rect x="7" y="14" width="1" height="6" fill="#6b7280" />
            <rect x="16" y="14" width="1" height="6" fill="#6b7280" />
            
            {/* Bridge cables */}
            <path d="M8 10 L12 6 L16 10" stroke="#6b7280" strokeWidth="0.5" fill="none" />
            <path d="M8 14 L12 18 L16 14" stroke="#6b7280" strokeWidth="0.5" fill="none" />
            
            {/* Road markings */}
            <rect x="11.5" y="11" width="1" height="0.5" fill="white" />
            <rect x="11.5" y="12.5" width="1" height="0.5" fill="white" />
            
            {/* Water underneath */}
            <path d="M2 20 Q6 18 10 20 T18 20 T22 20" stroke="#3b82f6" strokeWidth="1" fill="none" />
          </svg>
        );

      case 'tunnel':
        return (
          <svg
            width={size}
            height={size}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={className}
          >
            {/* Tunnel entrance */}
            <path d="M4 20 L4 12 Q4 8 8 8 L16 8 Q20 8 20 12 L20 20 Z" fill="#374151" />
            
            {/* Road inside tunnel */}
            <rect x="6" y="16" width="12" height="4" fill={color} />
            
            {/* Tunnel lights */}
            <circle cx="8" cy="10" r="0.5" fill="#fbbf24" />
            <circle cx="12" cy="10" r="0.5" fill="#fbbf24" />
            <circle cx="16" cy="10" r="0.5" fill="#fbbf24" />
            
            {/* Road markings */}
            <rect x="11.5" y="17" width="1" height="0.5" fill="white" />
            <rect x="11.5" y="18.5" width="1" height="0.5" fill="white" />
            
            {/* Tunnel walls */}
            <rect x="4" y="8" width="1" height="12" fill="#1f2937" />
            <rect x="19" y="8" width="1" height="12" fill="#1f2937" />
          </svg>
        );

      default:
        return renderRoadVariant();
    }
  };

  return renderRoadVariant();
};

// Additional road-themed icon components
export const RoadIcon: React.FC<{ className?: string; size?: number }> = ({ className, size = 24 }) => (
  <RoadSVG variant="road" className={className} size={size} />
);

export const HighwayIcon: React.FC<{ className?: string; size?: number }> = ({ className, size = 24 }) => (
  <RoadSVG variant="highway" className={className} size={size} />
);

export const IntersectionIcon: React.FC<{ className?: string; size?: number }> = ({ className, size = 24 }) => (
  <RoadSVG variant="intersection" className={className} size={size} />
);

export const BridgeIcon: React.FC<{ className?: string; size?: number }> = ({ className, size = 24 }) => (
  <RoadSVG variant="bridge" className={className} size={size} />
);

export const TunnelIcon: React.FC<{ className?: string; size?: number }> = ({ className, size = 24 }) => (
  <RoadSVG variant="tunnel" className={className} size={size} />
);

// Animated road SVG for loading states
export const AnimatedRoadSVG: React.FC<{ className?: string; size?: number }> = ({ className, size = 24 }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    {/* Road surface */}
    <rect x="6" y="4" width="12" height="16" fill="#f97316" rx="1" />
    
    {/* Animated center line */}
    <g>
      <rect x="11.5" y="5" width="1" height="2" fill="white">
        <animate attributeName="opacity" values="0;1;0" dur="1s" repeatCount="indefinite" begin="0s" />
      </rect>
      <rect x="11.5" y="8" width="1" height="2" fill="white">
        <animate attributeName="opacity" values="0;1;0" dur="1s" repeatCount="indefinite" begin="0.2s" />
      </rect>
      <rect x="11.5" y="11" width="1" height="2" fill="white">
        <animate attributeName="opacity" values="0;1;0" dur="1s" repeatCount="indefinite" begin="0.4s" />
      </rect>
      <rect x="11.5" y="14" width="1" height="2" fill="white">
        <animate attributeName="opacity" values="0;1;0" dur="1s" repeatCount="indefinite" begin="0.6s" />
      </rect>
      <rect x="11.5" y="17" width="1" height="2" fill="white">
        <animate attributeName="opacity" values="0;1;0" dur="1s" repeatCount="indefinite" begin="0.8s" />
      </rect>
    </g>
    
    {/* Side lines */}
    <rect x="6.5" y="4" width="0.5" height="16" fill="white" />
    <rect x="17" y="4" width="0.5" height="16" fill="white" />
  </svg>
);

export default RoadSVG;
