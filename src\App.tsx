
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import Index from "./pages/Index";
import Dashboard from "./pages/Dashboard";
import Mobile from "./pages/Mobile";
import Map from "./pages/Map";
import RoutePlanning from "./pages/RoutePlanning";
import ReportIssue from "./pages/ReportIssue";
import Analytics from "./pages/Analytics";
import SOS from "./pages/SOS";
import NotFound from "./pages/NotFound";
import QuickAuthSetup from "./components/auth/QuickAuthSetup";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/mobile" element={<Mobile />} />
            <Route path="/map" element={<Map />} />
            <Route path="/route-planning" element={<RoutePlanning />} />
            <Route path="/report" element={<ReportIssue />} />
            <Route path="/analytics" element={<Analytics />} />
            <Route path="/sos" element={<SOS />} />
            <Route path="/auth/setup" element={<QuickAuthSetup />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
