
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Camera, MapPin, AlertTriangle, Construction, Droplets, Car, Upload } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const ReportIssue = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [step, setStep] = useState(1);
  const [selectedIssue, setSelectedIssue] = useState("");
  const [description, setDescription] = useState("");
  const [location, setLocation] = useState("");
  const [photo, setPhoto] = useState<File | null>(null);

  const issueTypes = [
    {
      id: "pothole",
      label: "Pothole",
      icon: AlertTriangle,
      color: "bg-red-500",
      description: "Road surface damage or holes"
    },
    {
      id: "flood",
      label: "Flooding",
      icon: Droplets,
      color: "bg-blue-500",
      description: "Water accumulation blocking traffic"
    },
    {
      id: "obstruction",
      label: "Road Obstruction",
      icon: Car,
      color: "bg-orange-500",
      description: "Debris, fallen trees, or blocked lanes"
    },
    {
      id: "construction",
      label: "Construction Issue",
      icon: Construction,
      color: "bg-yellow-500",
      description: "Unsafe work zones or missing signs"
    }
  ];

  const handleSubmit = () => {
    toast({
      title: "Issue Reported Successfully!",
      description: "Thank you for helping make our roads safer. Authorities have been notified.",
    });
    navigate("/mobile");
  };

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setPhoto(file);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-road-charcoal-800 to-road-charcoal-900 text-white p-4">
        <div className="flex items-center gap-4 mb-4">
          <Button 
            variant="ghost" 
            size="sm" 
            className="text-white p-2"
            onClick={() => step > 1 ? setStep(step - 1) : navigate("/mobile")}
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div>
            <h1 className="text-xl font-bold">Report Road Issue</h1>
            <p className="text-road-yellow-300 text-sm">Help improve road safety</p>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="flex items-center gap-2">
          {[1, 2, 3].map((stepNum) => (
            <div key={stepNum} className="flex items-center">
              <div 
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step >= stepNum 
                    ? "bg-road-yellow-500 text-road-charcoal-900" 
                    : "bg-white/20 text-white"
                }`}
              >
                {stepNum}
              </div>
              {stepNum < 3 && (
                <div 
                  className={`w-8 h-1 mx-1 ${
                    step > stepNum ? "bg-road-yellow-500" : "bg-white/20"
                  }`}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      <div className="p-4">
        {/* Step 1: Select Issue Type */}
        {step === 1 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-road-charcoal-900">What type of issue are you reporting?</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {issueTypes.map((issue) => (
                <Card 
                  key={issue.id}
                  className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                    selectedIssue === issue.id 
                      ? "ring-2 ring-road-yellow-500 bg-road-yellow-50" 
                      : "hover:bg-gray-50"
                  }`}
                  onClick={() => setSelectedIssue(issue.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className={`w-12 h-12 ${issue.color} rounded-lg flex items-center justify-center`}>
                        <issue.icon className="w-6 h-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-road-charcoal-900">{issue.label}</h3>
                        <p className="text-sm text-gray-600">{issue.description}</p>
                      </div>
                      {selectedIssue === issue.id && (
                        <Badge className="bg-road-yellow-500 text-road-charcoal-900">Selected</Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              <Button 
                className="w-full mt-6 bg-road-yellow-500 hover:bg-road-yellow-600 text-road-charcoal-900 font-semibold"
                disabled={!selectedIssue}
                onClick={() => setStep(2)}
              >
                Continue
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Step 2: Add Photo and Description */}
        {step === 2 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-road-charcoal-900">Add Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Photo Upload */}
              <div>
                <Label className="text-sm font-medium text-road-charcoal-900 mb-3 block">
                  Photo (Optional but recommended)
                </Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  {photo ? (
                    <div className="space-y-3">
                      <div className="w-20 h-20 bg-green-100 rounded-lg mx-auto flex items-center justify-center">
                        <Camera className="w-8 h-8 text-green-600" />
                      </div>
                      <p className="text-sm text-green-600 font-medium">{photo.name}</p>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setPhoto(null)}
                      >
                        Remove Photo
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <div className="w-20 h-20 bg-gray-100 rounded-lg mx-auto flex items-center justify-center">
                        <Camera className="w-8 h-8 text-gray-400" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">Upload a photo</p>
                        <p className="text-xs text-gray-500">Clear images help authorities respond faster</p>
                      </div>
                      <div className="flex gap-2 justify-center">
                        <Button variant="outline" size="sm">
                          <Camera className="w-4 h-4 mr-2" />
                          Take Photo
                        </Button>
                        <Button variant="outline" size="sm">
                          <Upload className="w-4 h-4 mr-2" />
                          <label htmlFor="photo-upload" className="cursor-pointer">
                            Upload
                          </label>
                        </Button>
                      </div>
                      <input
                        id="photo-upload"
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={handlePhotoUpload}
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Description */}
              <div>
                <Label htmlFor="description" className="text-sm font-medium text-road-charcoal-900">
                  Description (Optional)
                </Label>
                <Textarea
                  id="description"
                  placeholder="Provide additional details about the issue..."
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="mt-2"
                  rows={4}
                />
              </div>

              <Button 
                className="w-full bg-road-yellow-500 hover:bg-road-yellow-600 text-road-charcoal-900 font-semibold"
                onClick={() => setStep(3)}
              >
                Continue
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Step 3: Location and Submit */}
        {step === 3 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-road-charcoal-900">Confirm Location</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* GPS Location */}
              <div>
                <Label className="text-sm font-medium text-road-charcoal-900 mb-3 block">
                  Location
                </Label>
                <div className="border border-gray-300 rounded-lg p-4 bg-road-blue-50">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-road-blue-500 rounded-lg flex items-center justify-center">
                      <MapPin className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium text-road-charcoal-900">Using GPS Location</p>
                      <p className="text-sm text-gray-600">-17.8252, 31.0335</p>
                    </div>
                  </div>
                  <p className="text-sm text-road-blue-700 bg-white p-2 rounded">
                    📍 2nd Street, Harare CBD, Zimbabwe
                  </p>
                </div>
                
                <div className="mt-3">
                  <Label htmlFor="manual-location" className="text-sm text-gray-600">
                    Or enter location manually:
                  </Label>
                  <Input
                    id="manual-location"
                    placeholder="Enter street address or landmark"
                    value={location}
                    onChange={(e) => setLocation(e.target.value)}
                    className="mt-1"
                  />
                </div>
              </div>

              {/* Summary */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-medium text-road-charcoal-900 mb-3">Report Summary</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Issue Type:</span>
                    <span className="font-medium">
                      {issueTypes.find(t => t.id === selectedIssue)?.label}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Photo:</span>
                    <span className="font-medium">{photo ? "Attached" : "None"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Description:</span>
                    <span className="font-medium">{description ? "Added" : "None"}</span>
                  </div>
                </div>
              </div>

              <Button 
                className="w-full bg-road-yellow-500 hover:bg-road-yellow-600 text-road-charcoal-900 font-semibold py-3 text-lg"
                onClick={handleSubmit}
              >
                Submit Report
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default ReportIssue;
