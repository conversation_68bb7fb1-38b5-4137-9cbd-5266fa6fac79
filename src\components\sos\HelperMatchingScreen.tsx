
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MapPin, Clock, Star, Phone, MessageSquare, Navigation } from "lucide-react";
import TrustScoreDisplay from "./TrustScoreDisplay";
import { SOSHelper } from "@/types/sos";

interface HelperMatchingScreenProps {
  helpers: SOSHelper[];
  onSelectHelper: (helper: SOSHelper) => void;
  countdown: number;
}

const HelperMatchingScreen = ({ helpers, onSelectHelper, countdown }: HelperMatchingScreenProps) => {
  const [selectedHelper, setSelectedHelper] = useState<SOSHelper | null>(null);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getDistanceColor = (distance: number) => {
    if (distance <= 2) return "text-green-600 bg-green-100";
    if (distance <= 5) return "text-yellow-600 bg-yellow-100";
    return "text-red-600 bg-red-100";
  };

  return (
    <div className="space-y-4">
      {/* Countdown Timer */}
      <Card className="border-red-200 bg-red-50">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
                <Clock className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-red-700">Emergency Response Active</h3>
                <p className="text-sm text-red-600">Auto-escalation in {formatTime(countdown)}</p>
              </div>
            </div>
            <Badge variant="destructive" className="font-bold">
              SOS ACTIVE
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Available Helpers */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="w-5 h-5 text-road-blue-500" />
            Nearby Helpers ({helpers.length})
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {helpers.map((helper) => (
            <Card 
              key={helper.id} 
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedHelper?.id === helper.id ? 'ring-2 ring-road-blue-500 bg-road-blue-50' : ''
              }`}
              onClick={() => setSelectedHelper(helper)}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h3 className="font-semibold text-road-charcoal-900">{helper.name}</h3>
                    <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                      <span className={`px-2 py-1 rounded-full font-medium ${getDistanceColor(helper.distance)}`}>
                        {helper.distance}km away
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        ETA: {helper.eta} min
                      </span>
                    </div>
                  </div>
                  <TrustScoreDisplay 
                    score={helper.trustScore}
                    rating={helper.rating}
                    assistanceCount={helper.assistanceCount}
                    badges={helper.badges}
                    size="sm"
                  />
                </div>

                {/* Helper Badges */}
                <div className="flex flex-wrap gap-1 mb-3">
                  {helper.badges.map((badge, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {badge.title}
                    </Badge>
                  ))}
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onSelectHelper(helper);
                    }}
                    className="flex-1"
                  >
                    <Navigation className="w-4 h-4 mr-1" />
                    Request Help
                  </Button>
                  <Button variant="outline" size="sm">
                    <Phone className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <MessageSquare className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}

          {helpers.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <MapPin className="w-12 h-12 mx-auto mb-3 text-gray-300" />
              <h3 className="font-medium mb-2">No helpers nearby</h3>
              <p className="text-sm">Escalating to emergency services...</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default HelperMatchingScreen;
