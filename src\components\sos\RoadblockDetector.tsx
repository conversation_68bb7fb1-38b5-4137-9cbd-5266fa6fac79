
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert<PERSON>riangle, Shield, MapPin, Clock, Users } from "lucide-react";
import { RoadblockInfo } from "@/types/sos";

interface RoadblockDetectorProps {
  location: { lat: number; lng: number };
  onRoadblockDetected: (roadblock: RoadblockInfo) => void;
}

const RoadblockDetector = ({ location, onRoadblockDetected }: RoadblockDetectorProps) => {
  const [nearbyRoadblocks, setNearbyRoadblocks] = useState<RoadblockInfo[]>([]);
  const [isScanning, setIsScanning] = useState(true);

  useEffect(() => {
    // Simulate AI scanning for nearby roadblocks
    const timer = setTimeout(() => {
      const mockRoadblocks: RoadblockInfo[] = [
        {
          type: 'police',
          reportedBy: 'ZRP Traffic Division',
          verificationStatus: 'confirmed',
          activeTime: new Date(Date.now() - 30 * 60 * 1000), // 30 mins ago
          estimatedDuration: 60,
          description: 'Routine traffic checkpoint on A1 Highway',
          severity: 'medium'
        },
        {
          type: 'traffic',
          reportedBy: 'Community Helper',
          verificationStatus: 'unverified',
          activeTime: new Date(Date.now() - 15 * 60 * 1000), // 15 mins ago
          description: 'Heavy traffic buildup near Borrowdale Road intersection',
          severity: 'high'
        }
      ];
      setNearbyRoadblocks(mockRoadblocks);
      setIsScanning(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, [location]);

  const getRoadblockIcon = (type: string) => {
    switch (type) {
      case 'police': return <Shield className="w-4 h-4" />;
      case 'military': return <AlertTriangle className="w-4 h-4" />;
      case 'traffic': return <Users className="w-4 h-4" />;
      default: return <MapPin className="w-4 h-4" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-500 text-red-50';
      case 'medium': return 'bg-orange-500 text-orange-50';
      case 'low': return 'bg-green-500 text-green-50';
      default: return 'bg-gray-500 text-gray-50';
    }
  };

  const getVerificationColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-700';
      case 'disputed': return 'bg-red-100 text-red-700';
      default: return 'bg-yellow-100 text-yellow-700';
    }
  };

  if (isScanning) {
    return (
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center animate-pulse">
              <Shield className="w-4 h-4 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-blue-700">Scanning for Roadblocks</h3>
              <p className="text-sm text-blue-600">AI checking traffic patterns and police activity...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="w-5 h-5 text-orange-500" />
          Roadblock Alert System
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {nearbyRoadblocks.length > 0 ? (
          <>
            <p className="text-sm text-gray-600">
              {nearbyRoadblocks.length} roadblock(s) detected within 10km radius
            </p>
            {nearbyRoadblocks.map((roadblock, index) => (
              <Card key={index} className="border-orange-200">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      {getRoadblockIcon(roadblock.type)}
                      <span className="font-semibold capitalize">{roadblock.type} Roadblock</span>
                    </div>
                    <div className="flex gap-2">
                      <Badge className={getSeverityColor(roadblock.severity)}>
                        {roadblock.severity.toUpperCase()}
                      </Badge>
                      <Badge variant="secondary" className={getVerificationColor(roadblock.verificationStatus)}>
                        {roadblock.verificationStatus}
                      </Badge>
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-3">{roadblock.description}</p>
                  
                  <div className="flex items-center gap-4 text-xs text-gray-500 mb-3">
                    <span className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      Active for {Math.floor((Date.now() - roadblock.activeTime.getTime()) / (1000 * 60))} min
                    </span>
                    {roadblock.estimatedDuration && (
                      <span>Est. {roadblock.estimatedDuration} min remaining</span>
                    )}
                  </div>

                  <div className="flex gap-2">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => onRoadblockDetected(roadblock)}
                      className="flex-1"
                    >
                      Share with Helpers
                    </Button>
                    <Button variant="outline" size="sm">
                      Report Update
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </>
        ) : (
          <div className="text-center py-4 text-gray-500">
            <Shield className="w-8 h-8 mx-auto mb-2 text-gray-300" />
            <p className="text-sm">No roadblocks detected in your area</p>
            <p className="text-xs">AI monitoring continues in background</p>
          </div>
        )}

        <Button 
          variant="outline" 
          className="w-full" 
          onClick={() => {
            const newRoadblock: RoadblockInfo = {
              type: 'police',
              reportedBy: 'Current User',
              verificationStatus: 'unverified',
              activeTime: new Date(),
              description: 'User-reported roadblock',
              severity: 'medium'
            };
            onRoadblockDetected(newRoadblock);
          }}
        >
          <AlertTriangle className="w-4 h-4 mr-2" />
          Report New Roadblock
        </Button>
      </CardContent>
    </Card>
  );
};

export default RoadblockDetector;
