
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  ArrowLeft, 
  TrendingUp, 
  BarChart3, 
  PieChart, 
  MapPin, 
  Download,
  Calendar,
  Filter
} from "lucide-react";
import { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart as RechartsPieChart, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';

const Analytics = () => {
  const navigate = useNavigate();
  const [timeRange, setTimeRange] = useState("7days");
  const [selectedDistrict, setSelectedDistrict] = useState("all");

  // Sample data for charts
  const dailyReports = [
    { date: '2024-01-20', reports: 12, resolved: 8 },
    { date: '2024-01-21', reports: 15, resolved: 10 },
    { date: '2024-01-22', reports: 8, resolved: 12 },
    { date: '2024-01-23', reports: 18, resolved: 14 },
    { date: '2024-01-24', reports: 22, resolved: 16 },
    { date: '2024-01-25', reports: 19, resolved: 20 },
    { date: '2024-01-26', reports: 25, resolved: 18 },
  ];

  const issueTypes = [
    { name: 'Potholes', value: 45, color: '#ef4444' },
    { name: 'Flooding', value: 25, color: '#3b82f6' },
    { name: 'Obstructions', value: 20, color: '#f97316' },
    { name: 'Construction', value: 10, color: '#eab308' },
  ];

  const districtData = [
    { district: 'Harare CBD', reports: 85, resolved: 68, pending: 17 },
    { district: 'Chitungwiza', reports: 62, resolved: 45, pending: 17 },
    { district: 'Borrowdale', reports: 43, resolved: 38, pending: 5 },
    { district: 'Avondale', reports: 31, resolved: 28, pending: 3 },
    { district: 'Mbare', reports: 54, resolved: 41, pending: 13 },
  ];

  const responseTimeData = [
    { timeRange: '0-2h', count: 45 },
    { timeRange: '2-6h', count: 62 },
    { timeRange: '6-12h', count: 38 },
    { timeRange: '12-24h', count: 25 },
    { timeRange: '24h+', count: 12 },
  ];

  const priorityRoads = [
    { road: '2nd Street (CBD)', issues: 15, severity: 'High', lastReport: '2 hours ago' },
    { road: 'Chitungwiza Road', issues: 12, severity: 'Medium', lastReport: '4 hours ago' },
    { road: 'Borrowdale Road', issues: 8, severity: 'Low', lastReport: '1 day ago' },
    { road: 'Airport Road', issues: 6, severity: 'Medium', lastReport: '6 hours ago' },
    { road: 'Samora Machel Ave', issues: 9, severity: 'High', lastReport: '3 hours ago' },
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-road-charcoal-800 to-road-charcoal-900 text-white">
        <div className="container mx-auto px-6 py-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                className="text-white p-2"
                onClick={() => navigate("/dashboard")}
              >
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div className="w-12 h-12 bg-road-yellow-500 rounded-2xl flex items-center justify-center">
                <BarChart3 className="w-7 h-7 text-road-charcoal-900" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">Analytics & Reports</h1>
                <p className="text-road-yellow-300">Data-driven insights for infrastructure management</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-40 bg-white/10 border-white/20 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7days">Last 7 days</SelectItem>
                  <SelectItem value="30days">Last 30 days</SelectItem>
                  <SelectItem value="90days">Last 90 days</SelectItem>
                  <SelectItem value="1year">Last year</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-road-charcoal-900">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-8">
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid grid-cols-4 w-full max-w-lg">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
            <TabsTrigger value="districts">Districts</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold text-road-charcoal-900 mb-2">1,247</div>
                  <div className="text-sm text-gray-600">Total Reports</div>
                  <div className="text-sm text-green-600 font-medium">+12% this week</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold text-green-600 mb-2">71%</div>
                  <div className="text-sm text-gray-600">Resolution Rate</div>
                  <div className="text-sm text-green-600 font-medium">+5% improvement</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2">2.4h</div>
                  <div className="text-sm text-gray-600">Avg Response Time</div>
                  <div className="text-sm text-green-600 font-medium">-15% faster</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold text-yellow-600 mb-2">356</div>
                  <div className="text-sm text-gray-600">Pending Issues</div>
                  <div className="text-sm text-red-600 font-medium">+8% this week</div>
                </CardContent>
              </Card>
            </div>

            <div className="grid lg:grid-cols-2 gap-6">
              {/* Daily Reports Trend */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    Daily Reports vs Resolutions
                  </CardTitle>
                  <CardDescription>7-day trend of reported and resolved issues</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={dailyReports}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line type="monotone" dataKey="reports" stroke="#ef4444" strokeWidth={2} name="Reports" />
                        <Line type="monotone" dataKey="resolved" stroke="#22c55e" strokeWidth={2} name="Resolved" />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              {/* Issue Types Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <PieChart className="w-5 h-5" />
                    Issue Type Distribution
                  </CardTitle>
                  <CardDescription>Breakdown of reported issue categories</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <RechartsPieChart>
                        <Tooltip />
                        <RechartsPieChart data={issueTypes} cx="50%" cy="50%" outerRadius={80}>
                          {issueTypes.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </RechartsPieChart>
                        <Legend />
                      </RechartsPieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="trends" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Monthly Trends</CardTitle>
                <CardDescription>Long-term patterns in road issue reporting and resolution</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={dailyReports}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Area type="monotone" dataKey="reports" stackId="1" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.6} />
                      <Area type="monotone" dataKey="resolved" stackId="1" stroke="#22c55e" fill="#22c55e" fillOpacity={0.6} />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Response Time Distribution</CardTitle>
                <CardDescription>How quickly issues are being addressed</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={responseTimeData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="timeRange" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="count" fill="#eab308" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="districts" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="w-5 h-5" />
                  District Performance Overview
                </CardTitle>
                <CardDescription>Reports and resolution rates by district</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={districtData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="district" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="reports" fill="#3b82f6" name="Total Reports" />
                      <Bar dataKey="resolved" fill="#22c55e" name="Resolved" />
                      <Bar dataKey="pending" fill="#ef4444" name="Pending" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Priority Roads</CardTitle>
                <CardDescription>Roads requiring immediate attention based on issue frequency</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {priorityRoads.map((road, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-semibold text-road-charcoal-900">{road.road}</h3>
                        <p className="text-sm text-gray-600">{road.issues} active issues • Last report: {road.lastReport}</p>
                      </div>
                      <Badge className={getSeverityColor(road.severity)}>
                        {road.severity}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance" className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>System Performance</CardTitle>
                  <CardDescription>Platform efficiency metrics</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Report Processing Speed</span>
                    <span className="text-lg font-bold text-green-600">98.5%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">User Engagement Rate</span>
                    <span className="text-lg font-bold text-blue-600">87.2%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Resolution Accuracy</span>
                    <span className="text-lg font-bold text-yellow-600">94.1%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">System Uptime</span>
                    <span className="text-lg font-bold text-green-600">99.8%</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Export Options</CardTitle>
                  <CardDescription>Download comprehensive reports</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button variant="outline" className="w-full justify-start">
                    <Download className="w-4 h-4 mr-2" />
                    Daily Summary Report (PDF)
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Download className="w-4 h-4 mr-2" />
                    Issue Data Export (CSV)
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Download className="w-4 h-4 mr-2" />
                    Performance Analytics (Excel)
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Download className="w-4 h-4 mr-2" />
                    Custom Report Builder
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Analytics;
