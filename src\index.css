
@import 'leaflet/dist/leaflet.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for map markers */
.custom-marker {
  background: transparent !important;
  border: none !important;
}

.custom-marker svg {
  display: block;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 47 96% 53%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 47 96% 53%;
    --radius: 0.75rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 47 96% 53%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 47 96% 53%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-montserrat;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Navigation Mode Animations */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes slideInFromTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInFromBottom {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Car marker animation */
.car-marker {
  animation: fadeIn 0.5s ease-in-out;
}

/* Issue marker animation */
.issue-marker {
  animation: pulse 2s infinite;
}

/* Navigation overlay animations */
.nav-overlay-top {
  animation: slideInFromTop 0.5s ease-out;
}

.nav-overlay-bottom {
  animation: slideInFromBottom 0.5s ease-out;
}

/* Smooth transitions for navigation elements */
.nav-transition {
  transition: all 0.3s ease-in-out;
}

/* Prevent map flickering */
.leaflet-container {
  background: #1a1a1a !important;
}

.leaflet-tile {
  transition: opacity 0.2s ease-in-out;
}

.leaflet-zoom-anim .leaflet-zoom-animated {
  transition: transform 0.25s cubic-bezier(0,0,0.25,1);
}

/* Optimize route rendering */
.leaflet-interactive {
  pointer-events: auto;
}

/* Smooth car movement */
.car-marker {
  transition: transform 0.8s ease-out;
  will-change: transform;
}

/* Optimize polyline rendering */
.leaflet-overlay-pane svg {
  pointer-events: none;
}

.leaflet-overlay-pane path {
  transition: stroke-dasharray 0.3s ease;
}

/* Navigation UI Z-Index Management */
.leaflet-container {
  z-index: 1 !important;
}

.leaflet-control-container {
  z-index: 800 !important;
}

.leaflet-popup-pane {
  z-index: 700 !important;
}

.leaflet-tooltip-pane {
  z-index: 650 !important;
}

.leaflet-shadow-pane {
  z-index: 600 !important;
}

.leaflet-marker-pane {
  z-index: 600 !important;
}

.leaflet-tile-pane {
  z-index: 200 !important;
}

.leaflet-overlay-pane {
  z-index: 400 !important;
}

/* Navigation overlay elements should always be on top */
.nav-overlay-top,
.nav-overlay-bottom {
  z-index: 2000 !important;
  position: relative;
}

/* Ensure navigation UI elements are always visible */
.navigation-ui {
  z-index: 2000 !important;
  position: relative;
}

/* Force navigation elements above everything */
.navigation-ui .absolute {
  z-index: 2000 !important;
}

.navigation-ui .card {
  z-index: 2001 !important;
}
