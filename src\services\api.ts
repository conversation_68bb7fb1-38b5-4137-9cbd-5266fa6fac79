/**
 * API Service Layer for RoadPulse
 * Handles all backend communication and replaces mock data
 */

import { RoadIssue } from '../components/InteractiveMap';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SOS<PERSON>elper } from '../types/sos';

// API Configuration
const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001/v1',
  timeout: 10000,
  retries: 3,
};

// Types for API requests/responses
export interface CreateIssueRequest {
  type: RoadIssue['type'];
  location_name: string;
  coordinates: [number, number];
  severity: RoadIssue['severity'];
  description: string;
  image?: File;
  // SOS specific fields
  sos_type?: string;
  help_needed?: string;
  urgency_level?: string;
}

export interface IssueFilters {
  type?: string;
  severity?: string;
  status?: string;
  lat?: number;
  lng?: number;
  radius?: number; // km
  limit?: number;
  offset?: number;
}

export interface CreateSOSRequest {
  location: {
    lat: number;
    lng: number;
    accuracy: number;
    address: string;
  };
  category: SOSAlert['category'];
  description?: string;
  urgency_level: 'low' | 'medium' | 'high' | 'critical';
}

export interface LocationUpdate {
  lat: number;
  lng: number;
  accuracy: number;
  heading?: number;
  speed?: number;
  is_helping?: boolean;
}

export interface AuthResponse {
  token: string;
  refresh_token: string;
  user: {
    id: string;
    email: string;
    name: string;
    phone?: string;
    profile_image_url?: string;
    trust_score: number;
    rating: number;
    is_verified: boolean;
  };
}

export interface ApiError {
  message: string;
  code: string;
  details?: any;
}

class APIService {
  private token: string | null = null;
  private refreshToken: string | null = null;

  constructor() {
    this.token = localStorage.getItem('auth_token');
    this.refreshToken = localStorage.getItem('refresh_token');
  }

  /**
   * Generic request method with error handling and retries
   */
  private async request<T>(
    endpoint: string, 
    options: RequestInit = {},
    retryCount = 0
  ): Promise<T> {
    const url = `${API_CONFIG.baseURL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
        ...options.headers,
      },
      timeout: API_CONFIG.timeout,
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      // Handle token refresh
      if (response.status === 401 && this.refreshToken && retryCount === 0) {
        await this.refreshAuthToken();
        return this.request(endpoint, options, retryCount + 1);
      }
      
      if (!response.ok) {
        const error: ApiError = await response.json().catch(() => ({
          message: response.statusText,
          code: response.status.toString()
        }));
        throw new Error(`API Error: ${error.message}`);
      }
      
      return response.json();
    } catch (error) {
      if (retryCount < API_CONFIG.retries && this.shouldRetry(error)) {
        await this.delay(1000 * (retryCount + 1)); // Exponential backoff
        return this.request(endpoint, options, retryCount + 1);
      }
      throw error;
    }
  }

  private shouldRetry(error: any): boolean {
    return error.name === 'TypeError' || // Network error
           error.message.includes('timeout') ||
           error.message.includes('500');
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Authentication Methods
   */
  async login(email: string, password: string): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });

    this.setTokens(response.token, response.refresh_token);
    return response;
  }

  async register(userData: {
    email: string;
    password: string;
    name: string;
    phone?: string;
  }): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });

    this.setTokens(response.token, response.refresh_token);
    return response;
  }

  /**
   * Google OAuth Authentication
   */
  async googleLogin(googleData: {
    id_token: string;
    google_id: string;
    email: string;
  }): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/google/login', {
      method: 'POST',
      body: JSON.stringify(googleData),
    });

    this.setTokens(response.token, response.refresh_token);
    return response;
  }

  async googleRegister(googleData: {
    id_token: string;
    user_data: {
      email: string;
      name: string;
      firstName: string;
      lastName: string;
      profileImage?: string;
      provider: string;
      providerId: string;
      location?: {
        country: string;
        city?: string;
      };
    };
  }): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/google/register', {
      method: 'POST',
      body: JSON.stringify(googleData),
    });

    this.setTokens(response.token, response.refresh_token);
    return response;
  }

  async refreshAuthToken(): Promise<void> {
    if (!this.refreshToken) throw new Error('No refresh token available');
    
    const response = await this.request<{ token: string }>('/auth/refresh', {
      method: 'POST',
      body: JSON.stringify({ refresh_token: this.refreshToken }),
    });
    
    this.token = response.token;
    localStorage.setItem('auth_token', response.token);
  }

  private setTokens(token: string, refreshToken: string): void {
    this.token = token;
    this.refreshToken = refreshToken;
    localStorage.setItem('auth_token', token);
    localStorage.setItem('refresh_token', refreshToken);
  }

  logout(): void {
    this.token = null;
    this.refreshToken = null;
    localStorage.removeItem('auth_token');
    localStorage.removeItem('refresh_token');
  }

  /**
   * Road Issues API
   */
  async getIssues(filters: IssueFilters = {}): Promise<RoadIssue[]> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) params.append(key, value.toString());
    });
    
    return this.request(`/issues?${params}`);
  }

  async getIssueById(id: string): Promise<RoadIssue> {
    return this.request(`/issues/${id}`);
  }

  async createIssue(issueData: CreateIssueRequest): Promise<RoadIssue> {
    // Handle image upload separately if present
    if (issueData.image) {
      const imageUrl = await this.uploadImage(issueData.image);
      const { image, ...dataWithoutImage } = issueData;
      return this.request('/issues', {
        method: 'POST',
        body: JSON.stringify({ ...dataWithoutImage, image_url: imageUrl }),
      });
    }

    return this.request('/issues', {
      method: 'POST',
      body: JSON.stringify(issueData),
    });
  }

  async updateIssue(id: string, updates: Partial<RoadIssue>): Promise<RoadIssue> {
    return this.request(`/issues/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async verifyIssue(id: string, verification: {
    type: 'confirm' | 'dispute' | 'update';
    notes?: string;
  }): Promise<void> {
    return this.request(`/issues/${id}/verify`, {
      method: 'POST',
      body: JSON.stringify(verification),
    });
  }

  async getNearbyIssues(lat: number, lng: number, radius = 5): Promise<RoadIssue[]> {
    return this.getIssues({ lat, lng, radius });
  }

  /**
   * SOS System API
   */
  async createSOS(sosData: CreateSOSRequest): Promise<SOSAlert> {
    return this.request('/sos', {
      method: 'POST',
      body: JSON.stringify(sosData),
    });
  }

  async getActiveSOSRequests(lat?: number, lng?: number, radius = 10): Promise<SOSAlert[]> {
    const params = new URLSearchParams();
    if (lat && lng) {
      params.append('lat', lat.toString());
      params.append('lng', lng.toString());
      params.append('radius', radius.toString());
    }
    
    return this.request(`/sos/active?${params}`);
  }

  async respondToSOS(sosId: string): Promise<void> {
    return this.request(`/sos/${sosId}/respond`, {
      method: 'POST',
    });
  }

  async updateSOSStatus(sosId: string, status: SOSAlert['status']): Promise<void> {
    return this.request(`/sos/${sosId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status }),
    });
  }

  async getSOSHelpers(sosId: string): Promise<SOSHelper[]> {
    return this.request(`/sos/${sosId}/helpers`);
  }

  /**
   * Location & Real-time API
   */
  async updateLocation(location: LocationUpdate): Promise<void> {
    return this.request('/location/update', {
      method: 'POST',
      body: JSON.stringify(location),
    });
  }

  async getNearbyHelpers(lat: number, lng: number, radius = 5): Promise<SOSHelper[]> {
    const params = new URLSearchParams({
      lat: lat.toString(),
      lng: lng.toString(),
      radius: radius.toString(),
    });
    
    return this.request(`/location/helpers?${params}`);
  }

  async startHelpingMode(): Promise<void> {
    return this.request('/location/start-helping', {
      method: 'POST',
    });
  }

  async stopHelpingMode(): Promise<void> {
    return this.request('/location/stop-helping', {
      method: 'POST',
    });
  }

  /**
   * User Management API
   */
  async getUserProfile(userId?: string): Promise<any> {
    const endpoint = userId ? `/users/${userId}` : '/auth/profile';
    return this.request(endpoint);
  }

  async updateProfile(updates: any): Promise<any> {
    return this.request('/auth/profile', {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async rateUser(userId: string, rating: number, feedback?: string): Promise<void> {
    return this.request(`/users/${userId}/rate`, {
      method: 'POST',
      body: JSON.stringify({ rating, feedback }),
    });
  }

  /**
   * Utility Methods
   */
  private async uploadImage(file: File): Promise<string> {
    const formData = new FormData();
    formData.append('image', file);
    
    const response = await fetch(`${API_CONFIG.baseURL}/upload/image`, {
      method: 'POST',
      headers: {
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
      },
      body: formData,
    });
    
    if (!response.ok) {
      throw new Error('Failed to upload image');
    }
    
    const { url } = await response.json();
    return url;
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return this.request('/health');
  }
}

// Export singleton instance
export const apiService = new APIService();

// Export for testing
export { APIService };
