
export interface SOSAlert {
  id: string;
  userId: string;
  location: {
    lat: number;
    lng: number;
    accuracy: number;
    address: string;
  };
  timestamp: Date;
  status: 'active' | 'responded' | 'resolved' | 'escalated' | 'cancelled';
  riskScore: number;
  fraudProbability: number;
  description?: string;
  category: 'accident' | 'breakdown' | 'medical' | 'security' | 'roadblock' | 'other';
  helpers: SOSHelper[];
  escalationHistory: EscalationEvent[];
  roadblockInfo?: RoadblockInfo;
}

export interface RoadblockInfo {
  type: 'police' | 'military' | 'traffic' | 'checkpoint';
  reportedBy: string;
  verificationStatus: 'unverified' | 'confirmed' | 'disputed';
  activeTime: Date;
  estimatedDuration?: number; // minutes
  description?: string;
  severity: 'low' | 'medium' | 'high';
}

export interface SOSHelper {
  id: string;
  name: string;
  trustScore: number;
  location: {
    lat: number;
    lng: number;
  };
  distance: number;
  responseTime: number;
  rating: number;
  assistanceCount: number;
  badges: TrustBadge[];
  eta: number;
}

export interface TrustBadge {
  type: 'gold_guardian' | 'road_hero' | 'verified_helper' | 'rapid_responder' | 'roadblock_reporter';
  title: string;
  description: string;
  earnedDate: Date;
  count?: number;
}

export interface EscalationEvent {
  timestamp: Date;
  type: 'helper_alert' | 'admin_review' | 'authority_notification' | 'ambulance_dispatch' | 'roadblock_alert';
  status: 'pending' | 'completed' | 'failed';
  details: string;
}

export interface UbuntuPoints {
  total: number;
  earned: number;
  redeemed: number;
  history: PointTransaction[];
}

export interface PointTransaction {
  id: string;
  type: 'earned' | 'redeemed';
  amount: number;
  description: string;
  timestamp: Date;
  relatedSOSId?: string;
}
