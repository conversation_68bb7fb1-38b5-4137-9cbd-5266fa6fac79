
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Shield, Star, Award, Zap, AlertTriangle } from "lucide-react";
import { TrustBadge } from "@/types/sos";

interface TrustScoreDisplayProps {
  score: number;
  rating: number;
  assistanceCount: number;
  badges: TrustBadge[];
  size?: 'sm' | 'md' | 'lg';
}

const TrustScoreDisplay = ({ 
  score, 
  rating, 
  assistanceCount, 
  badges, 
  size = 'md' 
}: TrustScoreDisplayProps) => {
  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600 bg-green-100";
    if (score >= 70) return "text-yellow-600 bg-yellow-100";
    return "text-red-600 bg-red-100";
  };

  const getBadgeIcon = (type: string) => {
    switch (type) {
      case 'gold_guardian': return <Award className="w-3 h-3" />;
      case 'road_hero': return <Shield className="w-3 h-3" />;
      case 'verified_helper': return <Star className="w-3 h-3" />;
      case 'rapid_responder': return <Zap className="w-3 h-3" />;
      case 'roadblock_reporter': return <AlertTriangle className="w-3 h-3" />;
      default: return <Shield className="w-3 h-3" />;
    }
  };

  if (size === 'sm') {
    return (
      <div className="flex items-center gap-2">
        <div className={`px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(score)}`}>
          {score}% Trust
        </div>
        <div className="flex items-center gap-1 text-xs">
          <Star className="w-3 h-3 text-yellow-500" />
          <span>{rating.toFixed(1)}</span>
        </div>
      </div>
    );
  }

  return (
    <Card className="border-2 border-road-blue-200">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Shield className="w-5 h-5 text-road-blue-500" />
            <span className="font-semibold text-road-charcoal-900">Trust Score</span>
          </div>
          <div className={`px-3 py-1 rounded-full text-sm font-bold ${getScoreColor(score)}`}>
            {score}%
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-3">
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Star className="w-4 h-4 text-yellow-500" />
              <span className="font-semibold">{rating.toFixed(1)}</span>
            </div>
            <p className="text-xs text-gray-600">Rating</p>
          </div>
          <div className="text-center">
            <div className="font-semibold">{assistanceCount}</div>
            <p className="text-xs text-gray-600">Assists</p>
          </div>
        </div>

        {badges.length > 0 && (
          <div className="space-y-2">
            <p className="text-xs font-medium text-gray-700">Achievements</p>
            <div className="flex flex-wrap gap-1">
              {badges.slice(0, 3).map((badge, index) => (
                <Badge 
                  key={index} 
                  variant="secondary" 
                  className="text-xs flex items-center gap-1"
                >
                  {getBadgeIcon(badge.type)}
                  {badge.title}
                  {badge.count && <span>({badge.count})</span>}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TrustScoreDisplay;
