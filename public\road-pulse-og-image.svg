<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#374151;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="roadGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#6b7280;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bgGradient)"/>
  
  <!-- Road network pattern -->
  <g opacity="0.3">
    <!-- Main highway -->
    <rect x="0" y="280" width="1200" height="70" fill="url(#roadGradient)" rx="5"/>
    
    <!-- Road markings -->
    <rect x="50" y="310" width="40" height="4" fill="white" opacity="0.8"/>
    <rect x="120" y="310" width="40" height="4" fill="white" opacity="0.8"/>
    <rect x="190" y="310" width="40" height="4" fill="white" opacity="0.8"/>
    <rect x="260" y="310" width="40" height="4" fill="white" opacity="0.8"/>
    <rect x="330" y="310" width="40" height="4" fill="white" opacity="0.8"/>
    <rect x="400" y="310" width="40" height="4" fill="white" opacity="0.8"/>
    <rect x="470" y="310" width="40" height="4" fill="white" opacity="0.8"/>
    <rect x="540" y="310" width="40" height="4" fill="white" opacity="0.8"/>
    <rect x="610" y="310" width="40" height="4" fill="white" opacity="0.8"/>
    <rect x="680" y="310" width="40" height="4" fill="white" opacity="0.8"/>
    <rect x="750" y="310" width="40" height="4" fill="white" opacity="0.8"/>
    <rect x="820" y="310" width="40" height="4" fill="white" opacity="0.8"/>
    <rect x="890" y="310" width="40" height="4" fill="white" opacity="0.8"/>
    <rect x="960" y="310" width="40" height="4" fill="white" opacity="0.8"/>
    <rect x="1030" y="310" width="40" height="4" fill="white" opacity="0.8"/>
    <rect x="1100" y="310" width="40" height="4" fill="white" opacity="0.8"/>
    
    <!-- Vertical roads -->
    <rect x="300" y="0" width="50" height="630" fill="url(#roadGradient)" rx="3"/>
    <rect x="600" y="0" width="50" height="630" fill="url(#roadGradient)" rx="3"/>
    <rect x="900" y="0" width="50" height="630" fill="url(#roadGradient)" rx="3"/>
  </g>
  
  <!-- Logo circle -->
  <circle cx="200" cy="315" r="80" fill="#f97316" stroke="#fbbf24" stroke-width="4"/>
  
  <!-- Road icon in logo -->
  <rect x="160" y="280" width="80" height="70" fill="#374151" rx="8"/>
  <rect x="165" y="280" width="3" height="70" fill="white"/>
  <rect x="232" y="280" width="3" height="70" fill="white"/>
  <rect x="198" y="285" width="4" height="8" fill="white"/>
  <rect x="198" y="300" width="4" height="8" fill="white"/>
  <rect x="198" y="315" width="4" height="8" fill="white"/>
  <rect x="198" y="330" width="4" height="8" fill="white"/>
  <rect x="198" y="345" width="4" height="5" fill="white"/>
  
  <!-- Pulse effect around logo -->
  <circle cx="200" cy="315" r="90" fill="none" stroke="#fbbf24" stroke-width="2" opacity="0.6">
    <animate attributeName="r" values="90;110;90" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.6;0.2;0.6" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Main title -->
  <text x="350" y="280" font-family="Arial, sans-serif" font-size="72" font-weight="bold" fill="white">
    SmartRoadPulse
  </text>
  
  <!-- Subtitle -->
  <text x="350" y="330" font-family="Arial, sans-serif" font-size="36" fill="#fbbf24">
    Real-Time Road Intelligence
  </text>
  
  <!-- Feature highlights -->
  <text x="350" y="380" font-family="Arial, sans-serif" font-size="24" fill="#d1d5db">
    🚧 Live Issue Reporting  •  🗺️ Smart Navigation  •  🆘 Emergency SOS
  </text>
  
  <!-- Zimbabwe flag colors accent -->
  <rect x="350" y="420" width="500" height="8" fill="#10b981" rx="4"/>
  <rect x="350" y="435" width="500" height="8" fill="#fbbf24" rx="4"/>
  <rect x="350" y="450" width="500" height="8" fill="#ef4444" rx="4"/>
  
  <!-- Bottom tagline -->
  <text x="350" y="500" font-family="Arial, sans-serif" font-size="28" fill="#9ca3af">
    Empowering Zimbabwe's Road Safety Through Technology
  </text>
  
  <!-- Issue type icons -->
  <g transform="translate(950, 200)">
    <!-- Pothole icon -->
    <circle cx="0" cy="0" r="25" fill="#ef4444" opacity="0.8"/>
    <ellipse cx="0" cy="0" rx="12" ry="8" fill="white" opacity="0.9"/>
    <circle cx="-5" cy="-2" r="2" fill="#ef4444"/>
    <circle cx="5" cy="2" r="3" fill="#ef4444"/>
    
    <!-- Construction icon -->
    <circle cx="60" cy="0" r="25" fill="#f59e0b" opacity="0.8"/>
    <polygon points="60,-10 50,10 70,10" fill="white"/>
    <rect x="57" y="-5" width="6" height="3" fill="#f59e0b"/>
    
    <!-- Police icon -->
    <circle cx="120" cy="0" r="25" fill="#3b82f6" opacity="0.8"/>
    <polygon points="120,-12 110,8 130,8" fill="white"/>
    <circle cx="120" cy="-2" r="3" fill="#3b82f6"/>
    
    <!-- SOS icon -->
    <circle cx="180" cy="0" r="25" fill="#dc2626" opacity="0.8"/>
    <text x="180" y="8" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white" text-anchor="middle">
      SOS
    </text>
  </g>
  
  <!-- Animated pulse elements -->
  <circle cx="1050" cy="450" r="4" fill="#fbbf24">
    <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="1100" cy="400" r="3" fill="#10b981">
    <animate attributeName="opacity" values="1;0.3;1" dur="2.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="1000" cy="500" r="5" fill="#ef4444">
    <animate attributeName="opacity" values="1;0.3;1" dur="1.8s" repeatCount="indefinite"/>
  </circle>
</svg>
