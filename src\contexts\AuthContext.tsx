/**
 * Authentication Context for RoadPulse
 * Manages global authentication state and user session
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { googleAuthService, GoogleUser } from '../services/googleAuth';
import { apiService } from '../services/api';
import { dataService } from '../services/dataService';

// User interface for RoadPulse
export interface RoadPulseUser {
  id: string;
  email: string;
  name: string;
  firstName: string;
  lastName: string;
  profileImage?: string;
  phone?: string;
  trustScore: number;
  rating: number;
  isVerified: boolean;
  provider: 'google' | 'email';
  providerId?: string;
  location?: {
    country: string;
    city?: string;
  };
  preferences?: {
    notifications: boolean;
    locationSharing: boolean;
    emergencyContacts: string[];
  };
  createdAt: string;
  lastLoginAt: string;
}

// Authentication state interface
interface AuthState {
  user: RoadPulseUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Authentication context interface
interface AuthContextType extends AuthState {
  // Authentication methods
  signInWithGoogle: () => Promise<{ success: boolean; isNewUser: boolean }>;
  signInWithEmail: (email: string, password: string) => Promise<{ success: boolean }>;
  signUp: (userData: SignUpData) => Promise<{ success: boolean }>;
  signOut: () => Promise<void>;
  
  // User management
  updateProfile: (updates: Partial<RoadPulseUser>) => Promise<void>;
  refreshUser: () => Promise<void>;
  
  // Utility methods
  clearError: () => void;
  checkAuthStatus: () => Promise<void>;
}

// Sign up data interface
export interface SignUpData {
  email: string;
  password: string;
  name: string;
  firstName: string;
  lastName: string;
  phone?: string;
  agreeToTerms: boolean;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null
  });

  // Initialize authentication on app start
  useEffect(() => {
    initializeAuth();
  }, []);

  // Set up Google auth state listener
  useEffect(() => {
    const cleanup = googleAuthService.onAuthStateChanged((isSignedIn, googleUser) => {
      if (isSignedIn && googleUser) {
        handleGoogleAuthStateChange(googleUser);
      } else if (!isSignedIn && authState.user?.provider === 'google') {
        handleSignOut();
      }
    });

    return cleanup;
  }, [authState.user?.provider]);

  /**
   * Initialize authentication system
   */
  const initializeAuth = async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Initialize Google Auth (gracefully handle failures)
      try {
        await googleAuthService.initialize();
        console.log('✅ Google Auth initialized successfully');
      } catch (error: any) {
        console.warn('⚠️ Google Auth initialization failed, continuing with email auth only:', error);
        // Don't fail the entire auth initialization if Google Auth fails
      }

      // Check for existing session
      await checkAuthStatus();

      // Initialize data service with user ID if authenticated
      if (authState.user) {
        await dataService.initialize(authState.user.id);
      }
    } catch (error) {
      console.error('Auth initialization failed:', error);
      setAuthState(prev => ({
        ...prev,
        error: null, // Don't show error to user for Google Auth issues
        isLoading: false
      }));
    }
  };

  /**
   * Check current authentication status
   */
  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        setAuthState(prev => ({ ...prev, isLoading: false }));
        return;
      }

      // Check if we're in mock data mode
      const useMockData = import.meta.env.VITE_USE_MOCK_DATA === 'true';

      if (useMockData && token === 'mock-jwt-token') {
        // Use mock user data
        const mockUser = localStorage.getItem('mock_user');
        if (mockUser) {
          const userProfile = JSON.parse(mockUser);
          setAuthState({
            user: userProfile,
            isAuthenticated: true,
            isLoading: false,
            error: null
          });
          return;
        }
      } else if (!useMockData) {
        // Verify token with backend
        const userProfile = await apiService.getUserProfile();

        setAuthState({
          user: userProfile,
          isAuthenticated: true,
          isLoading: false,
          error: null
        });
        return;
      }

      // If we get here, token is invalid
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('mock_user');

      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null
      });
    } catch (error) {
      // Token is invalid, clear it
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('mock_user');

      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null
      });
    }
  };

  /**
   * Handle Google auth state changes
   */
  const handleGoogleAuthStateChange = async (googleUser: GoogleUser) => {
    try {
      // This is handled by the signInWithGoogle method
      // This listener is mainly for detecting external sign-outs
    } catch (error) {
      console.error('Google auth state change error:', error);
    }
  };

  /**
   * Sign in with Google
   */
  const signInWithGoogle = async (): Promise<{ success: boolean; isNewUser: boolean }> => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Check if Google Auth is available
      if (!googleAuthService.isInitialized) {
        setAuthState(prev => ({
          ...prev,
          error: 'Google Sign-In is not available. Please add http://localhost:8083 to your Google OAuth client or use email authentication.',
          isLoading: false
        }));
        return { success: false, isNewUser: false };
      }

      const { user: googleUser, isNewUser } = await googleAuthService.signIn();

      // Get the full user profile from our backend
      const userProfile = await apiService.getUserProfile();

      setAuthState({
        user: userProfile,
        isAuthenticated: true,
        isLoading: false,
        error: null
      });

      // Initialize data service
      await dataService.initialize(userProfile.id);

      return { success: true, isNewUser };
    } catch (error: any) {
      setAuthState(prev => ({
        ...prev,
        error: error.message || 'Google sign-in failed',
        isLoading: false
      }));
      return { success: false, isNewUser: false };
    }
  };

  /**
   * Sign in with email and password
   */
  const signInWithEmail = async (email: string, password: string): Promise<{ success: boolean }> => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Check if we're in mock data mode
      const useMockData = import.meta.env.VITE_USE_MOCK_DATA === 'true';

      if (useMockData) {
        // Check if user exists in localStorage
        const existingUser = localStorage.getItem('mock_user');
        if (existingUser) {
          const user = JSON.parse(existingUser);
          if (user.email === email) {
            setAuthState({
              user,
              isAuthenticated: true,
              isLoading: false,
              error: null
            });

            // Initialize data service
            await dataService.initialize(user.id);

            return { success: true };
          }
        }

        // User not found in mock data
        setAuthState(prev => ({
          ...prev,
          error: 'Invalid email or password',
          isLoading: false
        }));
        return { success: false };
      } else {
        // Use real API
        const response = await apiService.login(email, password);

        setAuthState({
          user: response.user,
          isAuthenticated: true,
          isLoading: false,
          error: null
        });

        // Initialize data service
        await dataService.initialize(response.user.id);

        return { success: true };
      }
    } catch (error: any) {
      setAuthState(prev => ({
        ...prev,
        error: error.message || 'Email sign-in failed',
        isLoading: false
      }));
      return { success: false };
    }
  };

  /**
   * Sign up with email
   */
  const signUp = async (userData: SignUpData): Promise<{ success: boolean }> => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Check if we're in mock data mode
      const useMockData = import.meta.env.VITE_USE_MOCK_DATA === 'true';

      if (useMockData) {
        // Create mock user for testing
        const mockUser: RoadPulseUser = {
          id: `mock-${Date.now()}`,
          email: userData.email,
          name: userData.name,
          firstName: userData.firstName,
          lastName: userData.lastName,
          phone: userData.phone,
          trustScore: 50,
          rating: 0.0,
          isVerified: false,
          provider: 'email',
          location: {
            country: 'Zimbabwe',
            city: 'Harare'
          },
          preferences: {
            notifications: true,
            locationSharing: true,
            emergencyContacts: []
          },
          createdAt: new Date().toISOString(),
          lastLoginAt: new Date().toISOString()
        };

        // Store mock user in localStorage
        localStorage.setItem('auth_token', 'mock-jwt-token');
        localStorage.setItem('mock_user', JSON.stringify(mockUser));

        setAuthState({
          user: mockUser,
          isAuthenticated: true,
          isLoading: false,
          error: null
        });

        // Initialize data service with mock data
        await dataService.initialize(mockUser.id);

        return { success: true };
      } else {
        // Use real API
        const response = await apiService.register({
          email: userData.email,
          password: userData.password,
          name: userData.name,
          phone: userData.phone
        });

        setAuthState({
          user: response.user,
          isAuthenticated: true,
          isLoading: false,
          error: null
        });

        // Initialize data service
        await dataService.initialize(response.user.id);

        return { success: true };
      }
    } catch (error: any) {
      setAuthState(prev => ({
        ...prev,
        error: error.message || 'Sign up failed',
        isLoading: false
      }));
      return { success: false };
    }
  };

  /**
   * Sign out
   */
  const signOut = async (): Promise<void> => {
    try {
      // Sign out from Google if applicable
      if (authState.user?.provider === 'google') {
        await googleAuthService.signOut();
      }
      
      // Sign out from our backend
      apiService.logout();
      
      handleSignOut();
    } catch (error) {
      console.error('Sign out error:', error);
      // Force sign out even if there's an error
      handleSignOut();
    }
  };

  /**
   * Handle sign out state update
   */
  const handleSignOut = () => {
    setAuthState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null
    });
  };

  /**
   * Update user profile
   */
  const updateProfile = async (updates: Partial<RoadPulseUser>): Promise<void> => {
    try {
      const updatedUser = await apiService.updateProfile(updates);
      
      setAuthState(prev => ({
        ...prev,
        user: updatedUser
      }));
    } catch (error: any) {
      setAuthState(prev => ({
        ...prev,
        error: error.message || 'Failed to update profile'
      }));
      throw error;
    }
  };

  /**
   * Refresh user data
   */
  const refreshUser = async (): Promise<void> => {
    try {
      if (!authState.isAuthenticated) return;
      
      const userProfile = await apiService.getUserProfile();
      
      setAuthState(prev => ({
        ...prev,
        user: userProfile
      }));
    } catch (error: any) {
      console.error('Failed to refresh user:', error);
    }
  };

  /**
   * Clear error state
   */
  const clearError = () => {
    setAuthState(prev => ({ ...prev, error: null }));
  };

  // Context value
  const contextValue: AuthContextType = {
    ...authState,
    signInWithGoogle,
    signInWithEmail,
    signUp,
    signOut,
    updateProfile,
    refreshUser,
    clearError,
    checkAuthStatus
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Export context for testing
export { AuthContext };
