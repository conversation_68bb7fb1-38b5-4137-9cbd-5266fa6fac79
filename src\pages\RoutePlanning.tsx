import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import RouteMap from '@/components/RouteMap';
import { RoadIssue } from '@/components/InteractiveMap';
import {
  ArrowLeft,
  Route,
  AlertTriangle,
  Clock,
  MapPin,
  Navigation,
  Shield,
  Camera,
  Construction,
  Droplets,
  Car,
  Share,
  Download,
  Bookmark
} from 'lucide-react';

// Sample road issues data for Zimbabwe
const defaultIssues: RoadIssue[] = [
  {
    id: '1',
    type: 'pothole',
    location: 'Samora Machel Avenue',
    coordinates: [-17.8292, 31.0522],
    severity: 'high',
    description: 'Large pothole causing traffic delays',
    reportedBy: 'Tendai M.',
    reportedAt: '2024-01-15T10:30:00Z',
    status: 'open',
    verified: true
  },
  {
    id: '2',
    type: 'construction',
    location: 'Robert Mugabe Road',
    coordinates: [-17.8200, 31.0450],
    severity: 'medium',
    description: 'Road maintenance work in progress',
    reportedBy: 'City Council',
    reportedAt: '2024-01-14T08:00:00Z',
    status: 'in-progress',
    verified: true
  },
  {
    id: '3',
    type: 'flooding',
    location: 'Harare Drive',
    coordinates: [-17.8400, 31.0600],
    severity: 'critical',
    description: 'Road flooded due to heavy rains',
    reportedBy: 'Grace K.',
    reportedAt: '2024-01-15T14:20:00Z',
    status: 'open',
    verified: true
  },
  {
    id: '4',
    type: 'police',
    location: 'Airport Road Checkpoint',
    coordinates: [-17.9000, 31.0800],
    severity: 'low',
    description: 'Traffic police checkpoint - document verification',
    reportedBy: 'Anonymous',
    reportedAt: '2024-01-15T16:00:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    lastSeen: '2024-01-15T16:00:00Z',
    policeType: 'checkpoint'
  },
  {
    id: '5',
    type: 'camera',
    location: 'Borrowdale Road Speed Camera',
    coordinates: [-17.8100, 31.0700],
    severity: 'low',
    description: 'Fixed speed enforcement camera',
    reportedBy: 'Traffic Authority',
    reportedAt: '2024-01-10T00:00:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    cameraType: 'speed'
  },
  {
    id: '6',
    type: 'accident',
    location: 'Chinhoyi Street Intersection',
    coordinates: [-17.8350, 31.0480],
    severity: 'high',
    description: 'Vehicle accident blocking one lane',
    reportedBy: 'Emergency Services',
    reportedAt: '2024-01-15T17:30:00Z',
    status: 'open',
    verified: true
  },
  // SOS Help Requests
  {
    id: '7',
    type: 'sos',
    location: 'Samora Machel Avenue CBD',
    coordinates: [-17.8292, 31.0522],
    severity: 'critical',
    description: 'Medical emergency - passenger feeling unwell',
    reportedBy: 'Tendai Mukamuri',
    reportedAt: '2024-01-15T18:00:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    sosType: 'medical',
    helpNeeded: 'Passenger feeling unwell, need medical help',
    urgencyLevel: 'critical',
    estimatedHelpers: 5,
    responseTime: 8,
    userProfile: {
      name: 'Tendai Mukamuri',
      phone: '+263 77 123 4567',
      rating: 4.9,
      trustScore: 95,
      profileImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      vehicleInfo: 'Blue Honda Fit (XYZ-789Z)'
    }
  },
  {
    id: '8',
    type: 'sos',
    location: 'Enterprise Road near Borrowdale',
    coordinates: [-17.8150, 31.0650],
    severity: 'high',
    description: 'Vehicle breakdown - car won\'t start',
    reportedBy: 'Chipo Madziva',
    reportedAt: '2024-01-15T18:15:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    sosType: 'breakdown',
    helpNeeded: 'Car won\'t start, need jump start or towing',
    urgencyLevel: 'high',
    estimatedHelpers: 3,
    responseTime: 15,
    userProfile: {
      name: 'Chipo Madziva',
      phone: '+263 77 234 5678',
      rating: 4.8,
      trustScore: 92,
      profileImage: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
      vehicleInfo: 'White Toyota Corolla (ABC-123Z)'
    }
  },
  {
    id: '9',
    type: 'sos',
    location: 'Chinhoyi Street near Market',
    coordinates: [-17.8320, 31.0500],
    severity: 'medium',
    description: 'Tire puncture - need spare tire help',
    reportedBy: 'Grace Nyathi',
    reportedAt: '2024-01-15T18:30:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    sosType: 'tire',
    helpNeeded: 'Front tire punctured, need spare tire help',
    urgencyLevel: 'medium',
    estimatedHelpers: 2,
    responseTime: 20,
    userProfile: {
      name: 'Grace Nyathi',
      phone: '+263 77 345 6789',
      rating: 4.6,
      trustScore: 88,
      profileImage: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
      vehicleInfo: 'Red Nissan March (DEF-456Z)'
    }
  },
  {
    id: '10',
    type: 'sos',
    location: 'Robert Mugabe Road near University',
    coordinates: [-17.8180, 31.0420],
    severity: 'medium',
    description: 'Out of petrol - need fuel assistance',
    reportedBy: 'Michael Chivasa',
    reportedAt: '2024-01-15T18:45:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    sosType: 'fuel',
    helpNeeded: 'Out of petrol, need fuel assistance',
    urgencyLevel: 'medium',
    estimatedHelpers: 4,
    responseTime: 25,
    userProfile: {
      name: 'Michael Chivasa',
      phone: '+263 77 456 7890',
      rating: 4.7,
      trustScore: 90,
      profileImage: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
      vehicleInfo: 'Silver Mazda Demio (GHI-789Z)'
    }
  }
];

interface RouteInfo {
  distance: number;
  duration: number;
  issuesOnRoute: RoadIssue[];
  routeCoordinates: [number, number][];
}

const RoutePlanning: React.FC = () => {
  const navigate = useNavigate();
  const [routeInfo, setRouteInfo] = useState<RouteInfo | null>(null);
  const [selectedTab, setSelectedTab] = useState('plan');

  const handleRouteCalculated = (route: RouteInfo) => {
    setRouteInfo(route);
    setSelectedTab('analysis');
  };

  const getSeverityColor = (severity: string) => {
    const colorMap = {
      low: 'bg-green-500',
      medium: 'bg-yellow-500',
      high: 'bg-orange-500',
      critical: 'bg-red-500'
    };
    return colorMap[severity as keyof typeof colorMap] || 'bg-gray-500';
  };

  const getIssueIcon = (type: string) => {
    const iconMap = {
      pothole: AlertTriangle,
      construction: Construction,
      flooding: Droplets,
      accident: Car,
      police: Shield,
      camera: Camera
    };
    return iconMap[type as keyof typeof iconMap] || AlertTriangle;
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const getRouteSafetyScore = () => {
    if (!routeInfo) return 0;

    const totalIssues = routeInfo.issuesOnRoute.length;
    const criticalIssues = routeInfo.issuesOnRoute.filter(i => i.severity === 'critical').length;
    const highIssues = routeInfo.issuesOnRoute.filter(i => i.severity === 'high').length;

    // Calculate safety score (0-100)
    let score = 100;
    score -= criticalIssues * 30;
    score -= highIssues * 20;
    score -= (totalIssues - criticalIssues - highIssues) * 10;

    return Math.max(0, score);
  };

  const getSafetyScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-zimbabwe-gold-50 to-zimbabwe-green-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => navigate(-1)}
                className="text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">🇿🇼 Smart Route Planning</h1>
                <p className="text-sm text-gray-600">Plan your journey with real-time problem detection</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Share className="w-4 h-4 mr-2" />
                Share Route
              </Button>
              <Button variant="outline" size="sm">
                <Bookmark className="w-4 h-4 mr-2" />
                Save Route
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="plan" className="flex items-center gap-2">
              <Route className="w-4 h-4" />
              Plan Route
            </TabsTrigger>
            <TabsTrigger value="analysis" className="flex items-center gap-2">
              <AlertTriangle className="w-4 h-4" />
              Route Analysis
            </TabsTrigger>
            <TabsTrigger value="alternatives" className="flex items-center gap-2">
              <Navigation className="w-4 h-4" />
              Alternative Routes
            </TabsTrigger>
          </TabsList>

          <TabsContent value="plan" className="space-y-6">
            <RouteMap
              height="600px"
              center={[-17.8292, 31.0522]}
              zoom={12}
              issues={defaultIssues}
              onRouteCalculated={handleRouteCalculated}
            />
          </TabsContent>

          <TabsContent value="analysis" className="space-y-6">
            {routeInfo ? (
              <div className="grid lg:grid-cols-3 gap-6">
                {/* Route Summary */}
                <div className="lg:col-span-1 space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <MapPin className="w-5 h-5" />
                        Route Summary
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-zimbabwe-gold-600">
                            {(routeInfo.distance / 1000).toFixed(1)}
                          </div>
                          <div className="text-sm text-gray-600">km</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-zimbabwe-gold-600">
                            {formatTime(routeInfo.duration)}
                          </div>
                          <div className="text-sm text-gray-600">duration</div>
                        </div>
                      </div>

                      <div className="text-center">
                        <div className={`text-3xl font-bold ${getSafetyScoreColor(getRouteSafetyScore())}`}>
                          {getRouteSafetyScore()}%
                        </div>
                        <div className="text-sm text-gray-600">Safety Score</div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <AlertTriangle className="w-5 h-5" />
                        Issues on Route ({routeInfo.issuesOnRoute.length})
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {routeInfo.issuesOnRoute.length > 0 ? (
                        <div className="space-y-3 max-h-96 overflow-y-auto">
                          {routeInfo.issuesOnRoute.map((issue) => {
                            const IconComponent = getIssueIcon(issue.type);
                            return (
                              <div key={issue.id} className="p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-start gap-3">
                                  <IconComponent className="w-5 h-5 mt-0.5 text-gray-600" />
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-1">
                                      <h4 className="font-medium text-sm">{issue.location}</h4>
                                      <Badge className={getSeverityColor(issue.severity) + ' text-white text-xs'}>
                                        {issue.severity}
                                      </Badge>
                                    </div>
                                    <p className="text-xs text-gray-600">{issue.description}</p>
                                    <div className="flex items-center gap-2 mt-2">
                                      <Clock className="w-3 h-3 text-gray-400" />
                                      <span className="text-xs text-gray-500">
                                        {new Date(issue.reportedAt).toLocaleDateString()}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      ) : (
                        <div className="text-center py-8">
                          <div className="text-green-600 mb-2">✅</div>
                          <p className="text-sm text-gray-600">No issues detected on this route!</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>

                {/* Map */}
                <div className="lg:col-span-2">
                  <RouteMap
                    height="600px"
                    center={[-17.8292, 31.0522]}
                    zoom={12}
                    issues={defaultIssues}
                    onRouteCalculated={handleRouteCalculated}
                  />
                </div>
              </div>
            ) : (
              <Card>
                <CardContent className="text-center py-12">
                  <Route className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-600 mb-2">No Route Planned</h3>
                  <p className="text-gray-500 mb-4">Plan a route first to see the analysis</p>
                  <Button onClick={() => setSelectedTab('plan')}>
                    Plan Route
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="alternatives" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Navigation className="w-5 h-5" />
                  Alternative Routes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <Navigation className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-600 mb-2">Coming Soon</h3>
                  <p className="text-gray-500">Alternative route suggestions will be available soon</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default RoutePlanning;
