/**
 * Quick Authentication Setup Component
 * Helps users set up Google OAuth quickly
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Alert, AlertDescription } from '../ui/alert';
import { Badge } from '../ui/badge';
import { CheckCircle, XCircle, AlertTriangle, ExternalLink, Copy } from 'lucide-react';
import { googleAuthService } from '../../services/googleAuth';

const QuickAuthSetup: React.FC = () => {
  const [setupStatus, setSetupStatus] = useState({
    googleClientId: false,
    googleApiLoaded: false,
    backendConnection: false,
    databaseReady: false
  });
  
  const [isChecking, setIsChecking] = useState(false);
  const [showInstructions, setShowInstructions] = useState(false);

  useEffect(() => {
    checkSetupStatus();
  }, []);

  const checkSetupStatus = async () => {
    setIsChecking(true);
    
    // Check Google Client ID
    const hasClientId = !!import.meta.env.VITE_GOOGLE_CLIENT_ID;
    
    // Check if Google API can be loaded
    let googleApiLoaded = false;
    try {
      await googleAuthService.initialize();
      googleApiLoaded = true;
    } catch (error) {
      console.log('Google API not ready:', error);
    }
    
    // Check backend connection (simplified)
    let backendConnection = false;
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:3001/v1'}/health`);
      backendConnection = response.ok;
    } catch (error) {
      console.log('Backend not ready:', error);
    }
    
    setSetupStatus({
      googleClientId: hasClientId,
      googleApiLoaded,
      backendConnection,
      databaseReady: backendConnection // Simplified check
    });
    
    setIsChecking(false);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getStatusIcon = (status: boolean) => {
    return status ? (
      <CheckCircle className="w-4 h-4 text-green-500" />
    ) : (
      <XCircle className="w-4 h-4 text-red-500" />
    );
  };

  const allReady = Object.values(setupStatus).every(status => status);

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔐 Authentication Setup Status
            <Badge variant={allReady ? "default" : "destructive"}>
              {allReady ? "Ready" : "Setup Required"}
            </Badge>
          </CardTitle>
          <CardDescription>
            Check your Google OAuth and authentication setup status
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Google Client ID */}
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-2">
                {getStatusIcon(setupStatus.googleClientId)}
                <span className="font-medium">Google Client ID</span>
              </div>
              <Badge variant={setupStatus.googleClientId ? "default" : "outline"}>
                {setupStatus.googleClientId ? "Configured" : "Missing"}
              </Badge>
            </div>

            {/* Google API */}
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-2">
                {getStatusIcon(setupStatus.googleApiLoaded)}
                <span className="font-medium">Google API</span>
              </div>
              <Badge variant={setupStatus.googleApiLoaded ? "default" : "outline"}>
                {setupStatus.googleApiLoaded ? "Loaded" : "Failed"}
              </Badge>
            </div>

            {/* Backend Connection */}
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-2">
                {getStatusIcon(setupStatus.backendConnection)}
                <span className="font-medium">Backend API</span>
              </div>
              <Badge variant={setupStatus.backendConnection ? "default" : "outline"}>
                {setupStatus.backendConnection ? "Connected" : "Offline"}
              </Badge>
            </div>

            {/* Database */}
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-2">
                {getStatusIcon(setupStatus.databaseReady)}
                <span className="font-medium">Database</span>
              </div>
              <Badge variant={setupStatus.databaseReady ? "default" : "outline"}>
                {setupStatus.databaseReady ? "Ready" : "Not Ready"}
              </Badge>
            </div>
          </div>

          <div className="flex gap-2">
            <Button onClick={checkSetupStatus} disabled={isChecking}>
              {isChecking ? "Checking..." : "Refresh Status"}
            </Button>
            <Button 
              variant="outline" 
              onClick={() => setShowInstructions(!showInstructions)}
            >
              {showInstructions ? "Hide" : "Show"} Setup Instructions
            </Button>
          </div>

          {!allReady && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Some components are not ready. Follow the setup instructions below to complete the configuration.
              </AlertDescription>
            </Alert>
          )}

          {allReady && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-700">
                🎉 All systems ready! Your authentication is fully configured.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {showInstructions && (
        <Card>
          <CardHeader>
            <CardTitle>🚀 Quick Setup Instructions</CardTitle>
            <CardDescription>
              Follow these steps to complete your authentication setup
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Step 1: Google OAuth */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                1. Google OAuth Setup
                {setupStatus.googleClientId ? (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-500" />
                )}
              </h3>
              
              <div className="pl-4 space-y-2">
                <p className="text-sm text-gray-600">
                  Create Google OAuth credentials and add them to your environment:
                </p>
                
                <div className="bg-gray-100 p-3 rounded-lg font-mono text-sm">
                  <div className="flex items-center justify-between">
                    <span>VITE_GOOGLE_CLIENT_ID=your_client_id_here</span>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => copyToClipboard('VITE_GOOGLE_CLIENT_ID=your_client_id_here')}
                    >
                      <Copy className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
                
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => window.open('https://console.cloud.google.com/apis/credentials', '_blank')}
                >
                  <ExternalLink className="w-3 h-3 mr-1" />
                  Open Google Cloud Console
                </Button>
              </div>
            </div>

            {/* Step 2: Backend Setup */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                2. Backend Setup
                {setupStatus.backendConnection ? (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-500" />
                )}
              </h3>
              
              <div className="pl-4 space-y-2">
                <p className="text-sm text-gray-600">
                  Start your backend server with the authentication routes:
                </p>
                
                <div className="bg-gray-100 p-3 rounded-lg font-mono text-sm space-y-1">
                  <div>cd backend</div>
                  <div>npm install</div>
                  <div>npm run dev</div>
                </div>
                
                <p className="text-xs text-gray-500">
                  Backend should be running on: {import.meta.env.VITE_API_URL || 'http://localhost:3001/v1'}
                </p>
              </div>
            </div>

            {/* Step 3: Database */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                3. Database Setup
                {setupStatus.databaseReady ? (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-500" />
                )}
              </h3>
              
              <div className="pl-4 space-y-2">
                <p className="text-sm text-gray-600">
                  Set up PostgreSQL database with the required schema:
                </p>
                
                <div className="bg-gray-100 p-3 rounded-lg font-mono text-sm">
                  <div>docker run --name roadpulse-db -e POSTGRES_PASSWORD=password -p 5432:5432 -d postgres</div>
                </div>
              </div>
            </div>

            {/* Step 4: Test */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">4. Test Authentication</h3>
              
              <div className="pl-4">
                <p className="text-sm text-gray-600 mb-2">
                  Once everything is set up, test the authentication flow:
                </p>
                
                <Button 
                  onClick={() => window.location.href = '/'}
                  disabled={!allReady}
                >
                  Test Google Sign-In
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default QuickAuthSetup;
