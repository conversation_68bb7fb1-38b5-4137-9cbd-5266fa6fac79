
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Coins, Fuel, Car, Star, Gift } from "lucide-react";
import { UbuntuPoints } from "@/types/sos";

interface UbuntuPointsDisplayProps {
  points: UbuntuPoints;
  onRedeem?: (type: string, cost: number) => void;
}

const UbuntuPointsDisplay = ({ points, onRedeem }: UbuntuPointsDisplayProps) => {
  const rewardOptions = [
    {
      id: 'fuel_voucher',
      title: 'Shell Fuel Voucher',
      description: '$10 fuel credit',
      cost: 1000,
      icon: Fuel,
      color: 'bg-yellow-500',
      sponsor: 'Shell'
    },
    {
      id: 'toll_discount',
      title: 'Toll Discount',
      description: '50% off next toll payment',
      cost: 500,
      icon: Car,
      color: 'bg-blue-500',
      sponsor: 'ZINARA'
    },
    {
      id: 'premium_features',
      title: 'Premium Features',
      description: '1 month hazard heatmaps',
      cost: 750,
      icon: Star,
      color: 'bg-purple-500',
      sponsor: 'SmartRoadPulse'
    }
  ];

  return (
    <div className="space-y-4">
      {/* Points Balance */}
      <Card className="border-road-yellow-200 bg-road-yellow-50">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-road-yellow-500 rounded-full flex items-center justify-center">
                <Coins className="w-6 h-6 text-road-charcoal-900" />
              </div>
              <div>
                <h3 className="font-bold text-xl text-road-charcoal-900">{points.total.toLocaleString()}</h3>
                <p className="text-sm text-road-yellow-700">Ubuntu Points Available</p>
              </div>
            </div>
            <Badge className="bg-road-yellow-500 text-road-charcoal-900 font-bold">
              +{points.earned} Earned
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Reward Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="w-5 h-5 text-road-blue-500" />
            Available Rewards
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {rewardOptions.map((reward) => (
            <Card key={reward.id} className="border border-gray-200">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-10 h-10 ${reward.color} rounded-lg flex items-center justify-center`}>
                      <reward.icon className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-road-charcoal-900">{reward.title}</h4>
                      <p className="text-sm text-gray-600">{reward.description}</p>
                      <Badge variant="outline" className="text-xs mt-1">
                        Sponsored by {reward.sponsor}
                      </Badge>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-road-charcoal-900 mb-1">
                      {reward.cost.toLocaleString()} pts
                    </div>
                    <Button 
                      size="sm"
                      disabled={points.total < reward.cost}
                      onClick={() => onRedeem?.(reward.id, reward.cost)}
                      className="bg-road-blue-500 hover:bg-road-blue-600"
                    >
                      Redeem
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {points.history.slice(0, 5).map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-0">
                <div>
                  <p className="font-medium text-sm">{transaction.description}</p>
                  <p className="text-xs text-gray-500">
                    {new Date(transaction.timestamp).toLocaleDateString()}
                  </p>
                </div>
                <span className={`font-bold ${transaction.type === 'earned' ? 'text-green-600' : 'text-red-600'}`}>
                  {transaction.type === 'earned' ? '+' : '-'}{transaction.amount}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UbuntuPointsDisplay;
