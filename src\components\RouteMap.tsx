import React, { useEffect, useState, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import 'leaflet-routing-machine/dist/leaflet-routing-machine.css';
import 'leaflet-routing-machine';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { AlertTriangle, Navigation, MapPin, Clock, Route, X, Search, Play } from 'lucide-react';
import { RoadIssue } from './InteractiveMap';
import NavigationMode from './NavigationMode';
import { getCurrentRoutingService, ROUTING_ERROR_MESSAGES, FALLBACK_CONFIG } from '@/config/routing';

// Fix for default markers
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface RouteMapProps {
  height?: string;
  center?: [number, number];
  zoom?: number;
  issues?: RoadIssue[];
  onRouteCalculated?: (routeInfo: RouteInfo) => void;
}

interface RouteInfo {
  distance: number;
  duration: number;
  issuesOnRoute: RoadIssue[];
  routeCoordinates: [number, number][];
}

interface RoutePoint {
  name: string;
  coordinates: [number, number];
}

// Comprehensive location database for Zimbabwe and global search
const zimbabweLocations: RoutePoint[] = [
  // Major Cities
  { name: "Harare CBD", coordinates: [-17.8292, 31.0522] },
  { name: "Bulawayo", coordinates: [-20.1500, 28.5833] },
  { name: "Chitungwiza", coordinates: [-18.0167, 31.0833] },
  { name: "Mutare", coordinates: [-18.9707, 32.6707] },
  { name: "Gweru", coordinates: [-19.4500, 29.8167] },
  { name: "Kwekwe", coordinates: [-18.9167, 29.8167] },
  { name: "Kadoma", coordinates: [-18.3333, 29.9167] },
  { name: "Masvingo", coordinates: [-20.0637, 30.8276] },
  { name: "Chinhoyi", coordinates: [-17.3667, 30.2000] },
  { name: "Marondera", coordinates: [-18.1833, 31.5500] },

  // Harare Areas & Suburbs
  { name: "Harare International Airport", coordinates: [-17.9318, 31.0928] },
  { name: "University of Zimbabwe", coordinates: [-17.7840, 31.0534] },
  { name: "Avondale Shopping Centre", coordinates: [-17.8047, 31.0364] },
  { name: "Eastgate Mall", coordinates: [-17.8611, 31.0847] },
  { name: "Borrowdale Village", coordinates: [-17.8100, 31.0700] },
  { name: "Sam Levy's Village", coordinates: [-17.8000, 31.0650] },
  { name: "Newlands Shopping Centre", coordinates: [-17.8200, 31.0400] },
  { name: "Westgate Shopping Centre", coordinates: [-17.8300, 31.0200] },
  { name: "Belgravia", coordinates: [-17.8100, 31.0400] },
  { name: "Mount Pleasant", coordinates: [-17.7900, 31.0600] },
  { name: "Highlands", coordinates: [-17.7800, 31.0500] },
  { name: "Greendale", coordinates: [-17.8200, 31.0300] },
  { name: "Marlborough", coordinates: [-17.8400, 31.0800] },
  { name: "Waterfalls", coordinates: [-17.8600, 31.0900] },
  { name: "Mbare", coordinates: [-17.8500, 31.0400] },
  { name: "Highfield", coordinates: [-17.8700, 31.0300] },
  { name: "Glen View", coordinates: [-17.8800, 31.0200] },
  { name: "Budiriro", coordinates: [-17.8900, 31.0100] },
  { name: "Warren Park", coordinates: [-17.8300, 31.0100] },
  { name: "Mufakose", coordinates: [-17.8400, 31.0000] },

  // Tourist Destinations
  { name: "Victoria Falls", coordinates: [-17.9243, 25.8572] },
  { name: "Great Zimbabwe", coordinates: [-20.2667, 30.9333] },
  { name: "Hwange National Park", coordinates: [-18.6297, 26.4050] },
  { name: "Mana Pools National Park", coordinates: [-15.7394, 29.3794] },
  { name: "Nyanga National Park", coordinates: [-18.2167, 32.7500] },
  { name: "Chimanimani National Park", coordinates: [-19.8000, 32.8667] },
  { name: "Lake Kariba", coordinates: [-16.5167, 28.8000] },
  { name: "Matopo National Park", coordinates: [-20.5500, 28.5000] },

  // Border Towns
  { name: "Beitbridge", coordinates: [-22.2167, 30.0000] },
  { name: "Plumtree", coordinates: [-20.4833, 27.8167] },
  { name: "Nyamapanda", coordinates: [-15.6333, 32.2000] },
  { name: "Chirundu", coordinates: [-16.0333, 28.8500] },
  { name: "Kazungula", coordinates: [-17.7833, 25.2667] },

  // Mining Towns
  { name: "Bindura", coordinates: [-17.3000, 31.3333] },
  { name: "Chegutu", coordinates: [-18.1333, 30.1500] },
  { name: "Shurugwi", coordinates: [-19.6667, 30.0000] },
  { name: "Zvishavane", coordinates: [-20.3333, 30.0667] },
  { name: "Redcliff", coordinates: [-19.0333, 29.7833] }
];

// Global major cities for international search
const globalLocations: RoutePoint[] = [
  // Africa
  { name: "Johannesburg, South Africa", coordinates: [-26.2041, 28.0473] },
  { name: "Cape Town, South Africa", coordinates: [-33.9249, 18.4241] },
  { name: "Lusaka, Zambia", coordinates: [-15.3875, 28.3228] },
  { name: "Gaborone, Botswana", coordinates: [-24.6282, 25.9231] },
  { name: "Maputo, Mozambique", coordinates: [-25.9692, 32.5732] },
  { name: "Nairobi, Kenya", coordinates: [-1.2921, 36.8219] },
  { name: "Lagos, Nigeria", coordinates: [6.5244, 3.3792] },
  { name: "Cairo, Egypt", coordinates: [30.0444, 31.2357] },

  // Europe
  { name: "London, UK", coordinates: [51.5074, -0.1278] },
  { name: "Paris, France", coordinates: [48.8566, 2.3522] },
  { name: "Berlin, Germany", coordinates: [52.5200, 13.4050] },
  { name: "Rome, Italy", coordinates: [41.9028, 12.4964] },
  { name: "Madrid, Spain", coordinates: [40.4168, -3.7038] },

  // Asia
  { name: "Tokyo, Japan", coordinates: [35.6762, 139.6503] },
  { name: "Beijing, China", coordinates: [39.9042, 116.4074] },
  { name: "Mumbai, India", coordinates: [19.0760, 72.8777] },
  { name: "Dubai, UAE", coordinates: [25.2048, 55.2708] },

  // Americas
  { name: "New York, USA", coordinates: [40.7128, -74.0060] },
  { name: "Los Angeles, USA", coordinates: [34.0522, -118.2437] },
  { name: "Toronto, Canada", coordinates: [43.6532, -79.3832] },
  { name: "São Paulo, Brazil", coordinates: [-23.5505, -46.6333] },

  // Oceania
  { name: "Sydney, Australia", coordinates: [-33.8688, 151.2093] },
  { name: "Auckland, New Zealand", coordinates: [-36.8485, 174.7633] }
];

// Combined location database
const allLocations = [...zimbabweLocations, ...globalLocations];

// Geocoding function for address search
const geocodeLocation = async (query: string): Promise<RoutePoint[]> => {
  try {
    // Check if input is coordinates (lat,lng format)
    const coordMatch = query.match(/^(-?\d+\.?\d*),\s*(-?\d+\.?\d*)$/);
    if (coordMatch) {
      const lat = parseFloat(coordMatch[1]);
      const lng = parseFloat(coordMatch[2]);
      if (lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
        return [{
          name: `Coordinates: ${lat.toFixed(4)}, ${lng.toFixed(4)}`,
          coordinates: [lat, lng]
        }];
      }
    }

    // Search in local database first
    const localResults = allLocations.filter(location =>
      location.name.toLowerCase().includes(query.toLowerCase())
    );

    if (localResults.length > 0) {
      return localResults.slice(0, 10); // Limit to 10 results
    }

    // If no local results, try Nominatim geocoding for global search
    const encodedQuery = encodeURIComponent(query);
    const response = await fetch(
      `https://nominatim.openstreetmap.org/search?format=json&q=${encodedQuery}&limit=5&addressdetails=1`,
      {
        headers: {
          'User-Agent': 'SmartRoadPulse/1.0 (Zimbabwe Road Monitoring)'
        }
      }
    );

    if (!response.ok) {
      throw new Error('Geocoding service unavailable');
    }

    const data = await response.json();

    return data.map((result: any) => ({
      name: result.display_name,
      coordinates: [parseFloat(result.lat), parseFloat(result.lon)]
    }));

  } catch (error) {
    console.warn('Geocoding error:', error);
    // Fallback to local search only
    return allLocations.filter(location =>
      location.name.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 5);
  }
};

// Enhanced search function with smart filtering
const searchLocations = (query: string): RoutePoint[] => {
  if (!query.trim()) return [];

  const searchTerm = query.toLowerCase().trim();

  // Prioritize Zimbabwe locations
  const zimbabweResults = zimbabweLocations.filter(location =>
    location.name.toLowerCase().includes(searchTerm)
  );

  // Add global results if Zimbabwe results are limited
  const globalResults = globalLocations.filter(location =>
    location.name.toLowerCase().includes(searchTerm)
  );

  // Combine results with Zimbabwe locations first
  const combinedResults = [...zimbabweResults, ...globalResults];

  // Sort by relevance (exact matches first, then starts with, then contains)
  return combinedResults.sort((a, b) => {
    const aName = a.name.toLowerCase();
    const bName = b.name.toLowerCase();

    // Exact match
    if (aName === searchTerm) return -1;
    if (bName === searchTerm) return 1;

    // Starts with
    if (aName.startsWith(searchTerm) && !bName.startsWith(searchTerm)) return -1;
    if (bName.startsWith(searchTerm) && !aName.startsWith(searchTerm)) return 1;

    // Zimbabwe locations priority
    const aIsZimbabwe = zimbabweLocations.includes(a);
    const bIsZimbabwe = zimbabweLocations.includes(b);
    if (aIsZimbabwe && !bIsZimbabwe) return -1;
    if (bIsZimbabwe && !aIsZimbabwe) return 1;

    return 0;
  }).slice(0, 10); // Limit to 10 results
};

// Routing control component
const RoutingControl: React.FC<{
  start: [number, number] | null;
  end: [number, number] | null;
  onRouteFound: (route: any) => void;
  issues: RoadIssue[];
}> = ({ start, end, onRouteFound, issues }) => {
  const map = useMap();
  const routingControlRef = useRef<any>(null);
  const routeFoundRef = useRef(false);

  useEffect(() => {
    if (!start || !end) {
      // Clean up existing control if no start/end points
      if (routingControlRef.current) {
        map.removeControl(routingControlRef.current);
        routingControlRef.current = null;
        routeFoundRef.current = false;
      }
      return;
    }

    // Only create new routing control if we don't have one or coordinates changed significantly
    const shouldCreateNew = !routingControlRef.current ||
      !routeFoundRef.current ||
      (routingControlRef.current && (
        Math.abs(routingControlRef.current.getWaypoints()[0].latLng.lat - start[0]) > 0.001 ||
        Math.abs(routingControlRef.current.getWaypoints()[0].latLng.lng - start[1]) > 0.001 ||
        Math.abs(routingControlRef.current.getWaypoints()[1].latLng.lat - end[0]) > 0.001 ||
        Math.abs(routingControlRef.current.getWaypoints()[1].latLng.lng - end[1]) > 0.001
      ));

    if (!shouldCreateNew) return;

    // Remove existing routing control
    if (routingControlRef.current) {
      map.removeControl(routingControlRef.current);
      routeFoundRef.current = false;
    }

    // Get current routing service configuration
    const routingService = getCurrentRoutingService();

    // Log routing service info
    console.log(`🗺️ Using routing service: ${routingService.name}`);
    console.log(`🔗 Service URL: ${routingService.serviceUrl}`);
    console.log(`🚗 Profile: ${routingService.profile}`);
    console.log(`⏱️ Timeout: ${routingService.timeout}ms`);

    if (!routingService.isProduction) {
      console.warn(`⚠️ ${routingService.description}`);
      if (routingService.usagePolicy) {
        console.warn(`📋 Usage Policy: ${routingService.usagePolicy}`);
      }
    }

    // Create new routing control with optimized settings
    const routingControl = (L as any).Routing.control({
      waypoints: [
        L.latLng(start[0], start[1]),
        L.latLng(end[0], end[1])
      ],
      routeWhileDragging: false, // Disable to reduce flickering
      addWaypoints: false,
      show: false, // Hide the instruction panel
      createMarker: function(i: number, waypoint: any) {
        const isStart = i === 0;
        return L.marker(waypoint.latLng, {
          icon: L.divIcon({
            html: `
              <div style="background: ${isStart ? '#22c55e' : '#ef4444'}; color: white; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; font-weight: bold; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);">
                ${isStart ? 'A' : 'B'}
              </div>
            `,
            className: 'custom-route-marker',
            iconSize: [30, 30],
            iconAnchor: [15, 15]
          })
        });
      },
      lineOptions: {
        styles: [
          { color: '#f97316', weight: 8, opacity: 0.9 },
          { color: '#ffffff', weight: 4, opacity: 1 }
        ]
      },
      router: (L as any).Routing.osrmv1({
        serviceUrl: routingService.serviceUrl,
        profile: routingService.profile,
        timeout: routingService.timeout,
        routingOptions: {
          alternatives: false,
          steps: true,
          geometries: 'geojson',
          overview: 'full',
          continue_straight: false
        }
      })
    });

    // Add to map
    routingControl.addTo(map);

    // Log initial waypoints
    console.log('🗺️ Routing control created with waypoints:');
    console.log(`📍 Start: [${start[0]}, ${start[1]}]`);
    console.log(`📍 End: [${end[0]}, ${end[1]}]`);

    // Handle route found event
    routingControl.on('routesfound', function(e: any) {
      if (routeFoundRef.current) return; // Prevent multiple calls

      try {
        console.log('✅ Route found successfully:', e);
        const routes = e.routes;
        const summary = routes[0].summary;
        const coordinates = routes[0].coordinates;

        console.log(`📊 Route details: ${(summary.totalDistance / 1000).toFixed(1)}km, ${Math.round(summary.totalTime / 60)}min`);
        console.log(`🗺️ Route coordinates: ${coordinates.length} points`);

        // Find issues along the route
        const issuesOnRoute = findIssuesAlongRoute(coordinates, issues);

        onRouteFound({
          distance: summary.totalDistance,
          duration: summary.totalTime,
          issuesOnRoute,
          routeCoordinates: coordinates.map((coord: any) => [coord.lat, coord.lng])
        });

        routeFoundRef.current = true;
      } catch (error) {
        console.error('❌ Route calculation error:', error);
        console.warn(ROUTING_ERROR_MESSAGES.NO_ROUTE_FOUND);

        // Fallback to simple straight line route
        console.log('🔄 Falling back to straight line route');
        const straightLineRoute = [start, end];
        onRouteFound({
          distance: calculateStraightLineDistance(start, end),
          duration: calculateEstimatedTime(start, end),
          issuesOnRoute: [],
          routeCoordinates: straightLineRoute
        });
        routeFoundRef.current = true;
      }
    });

    // Handle routing errors
    routingControl.on('routingerror', function(e: any) {
      console.error('❌ Routing service error:', e);
      console.error('🔍 Error details:', {
        error: e.error,
        message: e.error?.message,
        status: e.error?.status,
        url: e.error?.url
      });

      // Determine error type and show appropriate message
      let errorMessage = ROUTING_ERROR_MESSAGES.SERVICE_UNAVAILABLE;
      if (e.error && e.error.message) {
        if (e.error.message.includes('timeout')) {
          errorMessage = ROUTING_ERROR_MESSAGES.TIMEOUT;
        } else if (e.error.message.includes('rate')) {
          errorMessage = ROUTING_ERROR_MESSAGES.RATE_LIMITED;
        } else if (e.error.message.includes('CORS')) {
          errorMessage = 'CORS error - routing service blocked by browser';
        } else if (e.error.message.includes('network')) {
          errorMessage = 'Network error - check internet connection';
        }
      }

      console.warn(`⚠️ ${errorMessage}`);

      // Fallback to simple straight line route
      console.log('🔄 Falling back to straight line route due to routing error');
      const straightLineRoute = [start, end];
      onRouteFound({
        distance: calculateStraightLineDistance(start, end),
        duration: calculateEstimatedTime(start, end),
        issuesOnRoute: [],
        routeCoordinates: straightLineRoute
      });
      routeFoundRef.current = true;
    });

    routingControlRef.current = routingControl;

    return () => {
      if (routingControlRef.current) {
        try {
          map.removeControl(routingControlRef.current);
        } catch (e) {
          // Control might already be removed
        }
        routingControlRef.current = null;
        routeFoundRef.current = false;
      }
    };
  }, [start, end, map]); // Removed onRouteFound and issues from dependencies to prevent re-renders

  return null;
};

// Function to find issues along a route
const findIssuesAlongRoute = (routeCoordinates: any[], issues: RoadIssue[], bufferDistance = 0.005): RoadIssue[] => {
  return issues.filter(issue => {
    return routeCoordinates.some(coord => {
      const distance = Math.sqrt(
        Math.pow(coord.lat - issue.coordinates[0], 2) +
        Math.pow(coord.lng - issue.coordinates[1], 2)
      );
      return distance <= bufferDistance;
    });
  });
};

// Helper function to calculate straight line distance
const calculateStraightLineDistance = (start: [number, number], end: [number, number]): number => {
  const R = 6371000; // Earth's radius in meters
  const lat1 = start[0] * Math.PI / 180;
  const lat2 = end[0] * Math.PI / 180;
  const deltaLat = (end[0] - start[0]) * Math.PI / 180;
  const deltaLng = (end[1] - start[1]) * Math.PI / 180;

  const a = Math.sin(deltaLat/2) * Math.sin(deltaLat/2) +
          Math.cos(lat1) * Math.cos(lat2) *
          Math.sin(deltaLng/2) * Math.sin(deltaLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  return R * c; // Distance in meters
};

// Helper function to estimate travel time
const calculateEstimatedTime = (start: [number, number], end: [number, number]): number => {
  const distance = calculateStraightLineDistance(start, end);
  const averageSpeed = FALLBACK_CONFIG.averageSpeed; // km/h from config
  return (distance / 1000) / averageSpeed * 3600; // Time in seconds
};

const RouteMap: React.FC<RouteMapProps> = ({
  height = '500px',
  center = [-17.8292, 31.0522],
  zoom = 13,
  issues = [],
  onRouteCalculated
}) => {
  const [startPoint, setStartPoint] = useState<[number, number] | null>(null);
  const [endPoint, setEndPoint] = useState<[number, number] | null>(null);
  const [routeInfo, setRouteInfo] = useState<RouteInfo | null>(null);
  const [startSearchQuery, setStartSearchQuery] = useState('');
  const [endSearchQuery, setEndSearchQuery] = useState('');
  const [startSearchResults, setStartSearchResults] = useState<RoutePoint[]>([]);
  const [endSearchResults, setEndSearchResults] = useState<RoutePoint[]>([]);
  const [showStartResults, setShowStartResults] = useState(false);
  const [showEndResults, setShowEndResults] = useState(false);
  const [isNavigationMode, setIsNavigationMode] = useState(false);
  const [selectedStartLocation, setSelectedStartLocation] = useState<string>('');
  const [selectedEndLocation, setSelectedEndLocation] = useState<string>('');
  const [isGeocodingStart, setIsGeocodingStart] = useState(false);
  const [isGeocodingEnd, setIsGeocodingEnd] = useState(false);

  // Handle start location search
  useEffect(() => {
    if (startSearchQuery.trim()) {
      const results = searchLocations(startSearchQuery);
      setStartSearchResults(results);
    } else {
      setStartSearchResults([]);
    }
  }, [startSearchQuery]);

  // Handle end location search
  useEffect(() => {
    if (endSearchQuery.trim()) {
      const results = searchLocations(endSearchQuery);
      setEndSearchResults(results);
    } else {
      setEndSearchResults([]);
    }
  }, [endSearchQuery]);

  // Enhanced geocoding search with debounce
  const handleAdvancedSearch = async (query: string, isStart: boolean) => {
    if (!query.trim()) return;

    if (isStart) {
      setIsGeocodingStart(true);
    } else {
      setIsGeocodingEnd(true);
    }

    try {
      const results = await geocodeLocation(query);
      if (isStart) {
        setStartSearchResults(results);
        setShowStartResults(true);
      } else {
        setEndSearchResults(results);
        setShowEndResults(true);
      }
    } catch (error) {
      console.warn('Advanced search failed:', error);
    } finally {
      if (isStart) {
        setIsGeocodingStart(false);
      } else {
        setIsGeocodingEnd(false);
      }
    }
  };

  // Close search results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.search-container')) {
        setShowStartResults(false);
        setShowEndResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleGetCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const coords: [number, number] = [position.coords.latitude, position.coords.longitude];
          setStartPoint(coords);
          setStartSearchQuery('Current Location');
          setSelectedStartLocation('Current Location');
          setShowStartResults(false);
        },
        (error) => {
          console.error('Error getting location:', error);
          alert('Unable to get your location. Please search for a starting location.');
        }
      );
    } else {
      alert('Geolocation is not supported by this browser. Please search for a starting location.');
    }
  };

  const handleStartLocationSelect = (location: RoutePoint) => {
    setStartPoint(location.coordinates);
    setStartSearchQuery(location.name);
    setSelectedStartLocation(location.name);
    setShowStartResults(false);
  };

  const handleEndLocationSelect = (location: RoutePoint) => {
    setEndPoint(location.coordinates);
    setEndSearchQuery(location.name);
    setSelectedEndLocation(location.name);
    setShowEndResults(false);
  };

  const handleRouteFound = (route: RouteInfo) => {
    setRouteInfo(route);
    if (onRouteCalculated) {
      onRouteCalculated(route);
    }
  };

  const clearRoute = () => {
    setStartPoint(null);
    setEndPoint(null);
    setRouteInfo(null);
    setStartSearchQuery('');
    setEndSearchQuery('');
    setSelectedStartLocation('');
    setSelectedEndLocation('');
    setStartSearchResults([]);
    setEndSearchResults([]);
    setShowStartResults(false);
    setShowEndResults(false);
  };

  const startNavigation = () => {
    if (routeInfo && routeInfo.routeCoordinates.length > 0) {
      setIsNavigationMode(true);
    }
  };

  const exitNavigation = () => {
    setIsNavigationMode(false);
  };

  const getSeverityColor = (severity: string) => {
    const colorMap = {
      low: 'bg-green-500',
      medium: 'bg-yellow-500',
      high: 'bg-orange-500',
      critical: 'bg-red-500'
    };
    return colorMap[severity as keyof typeof colorMap] || 'bg-gray-500';
  };

  // Render navigation mode as full overlay if active
  const navigationOverlay = isNavigationMode && routeInfo ? (
    <div className="fixed inset-0 z-[9999] bg-black">
      <NavigationMode
        routeCoordinates={routeInfo.routeCoordinates}
        issues={issues}
        onExit={exitNavigation}
        destination={selectedEndLocation || 'Destination'}
        totalDistance={routeInfo.distance}
        totalDuration={routeInfo.duration}
        isFullScreen={true}
      />
    </div>
  ) : null;

  return (
    <div className="space-y-4">
      {/* Route Planning Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Route className="w-5 h-5" />
            Route Planning with Real-time Problem Detection
          </CardTitle>
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <div className={`w-2 h-2 rounded-full ${getCurrentRoutingService().isProduction ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
              <span>Routing: {getCurrentRoutingService().name}</span>
            </div>
            {!getCurrentRoutingService().isProduction && (
              <Badge variant="outline" className="text-xs">
                Development Mode
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid md:grid-cols-2 gap-4">
            {/* Starting Point Search */}
            <div className="relative search-container">
              <label className="text-sm font-medium mb-2 block">Starting Point</label>
              <div className="space-y-2">
                <div className="relative">
                  <Input
                    placeholder="Search starting location or use current location..."
                    value={startSearchQuery}
                    onChange={(e) => {
                      setStartSearchQuery(e.target.value);
                      setShowStartResults(true);
                    }}
                    onFocus={() => setShowStartResults(true)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && startSearchQuery.trim()) {
                        handleAdvancedSearch(startSearchQuery, true);
                      }
                    }}
                  />
                  <Search className="absolute right-3 top-3 w-4 h-4 text-gray-400" />
                  {isGeocodingStart && (
                    <div className="absolute right-10 top-3">
                      <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                    </div>
                  )}
                </div>
                <Button
                  onClick={handleGetCurrentLocation}
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                >
                  <Navigation className="w-4 h-4 mr-2" />
                  Use Current Location
                </Button>
              </div>

              {showStartResults && startSearchResults.length > 0 && (
                <div className="absolute top-full left-0 right-0 bg-white border rounded-md shadow-lg z-20 max-h-64 overflow-y-auto">
                  {startSearchResults.map((location, index) => (
                    <button
                      key={index}
                      className="w-full text-left px-3 py-2 hover:bg-gray-100 border-b last:border-b-0"
                      onClick={() => handleStartLocationSelect(location)}
                    >
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-green-500" />
                        <div>
                          <div className="text-sm font-medium">{location.name}</div>
                          <div className="text-xs text-gray-500">
                            {location.coordinates[0].toFixed(4)}, {location.coordinates[1].toFixed(4)}
                          </div>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Destination Search */}
            <div className="relative search-container">
              <label className="text-sm font-medium mb-2 block">Destination</label>
              <div className="relative">
                <Input
                  placeholder="Search destination worldwide..."
                  value={endSearchQuery}
                  onChange={(e) => {
                    setEndSearchQuery(e.target.value);
                    setShowEndResults(true);
                  }}
                  onFocus={() => setShowEndResults(true)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && endSearchQuery.trim()) {
                      handleAdvancedSearch(endSearchQuery, false);
                    }
                  }}
                />
                <Search className="absolute right-3 top-3 w-4 h-4 text-gray-400" />
                {isGeocodingEnd && (
                  <div className="absolute right-10 top-3">
                    <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                  </div>
                )}
              </div>

              {showEndResults && endSearchResults.length > 0 && (
                <div className="absolute top-full left-0 right-0 bg-white border rounded-md shadow-lg z-20 max-h-64 overflow-y-auto">
                  {endSearchResults.map((location, index) => (
                    <button
                      key={index}
                      className="w-full text-left px-3 py-2 hover:bg-gray-100 border-b last:border-b-0"
                      onClick={() => handleEndLocationSelect(location)}
                    >
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-red-500" />
                        <div>
                          <div className="text-sm font-medium">{location.name}</div>
                          <div className="text-xs text-gray-500">
                            {location.coordinates[0].toFixed(4)}, {location.coordinates[1].toFixed(4)}
                          </div>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Search Tips */}
          <div className="bg-blue-50 p-3 rounded-lg">
            <h4 className="text-sm font-medium text-blue-900 mb-1">🔍 Search Tips:</h4>
            <ul className="text-xs text-blue-700 space-y-1">
              <li>• Search by city name: "Bulawayo", "Victoria Falls", "London"</li>
              <li>• Use coordinates: "-17.8292, 31.0522"</li>
              <li>• Try addresses: "123 Main Street, Harare"</li>
              <li>• Press Enter for global search</li>
            </ul>
          </div>

          {/* Quick Test Route */}
          <div className="bg-green-50 p-3 rounded-lg">
            <h4 className="text-sm font-medium text-green-900 mb-2">🧪 Quick Test:</h4>
            <Button
              onClick={() => {
                setStartPoint([-17.8292, 31.0522]); // Harare CBD
                setEndPoint([-17.8100, 31.0700]); // Borrowdale
                setStartSearchQuery('Harare CBD');
                setEndSearchQuery('Borrowdale Village');
                setSelectedStartLocation('Harare CBD');
                setSelectedEndLocation('Borrowdale Village');
                setShowStartResults(false);
                setShowEndResults(false);
              }}
              variant="outline"
              size="sm"
              className="text-green-700 border-green-300 hover:bg-green-100"
            >
              Test Route: Harare CBD → Borrowdale
            </Button>
          </div>

          {(startPoint || endPoint) && (
            <div className="flex gap-2">
              <Button onClick={clearRoute} variant="outline" size="sm">
                <X className="w-4 h-4 mr-2" />
                Clear Route
              </Button>
              {routeInfo && routeInfo.routeCoordinates.length > 0 && (
                <Button
                  onClick={startNavigation}
                  className="bg-orange-500 hover:bg-orange-600 text-white"
                  size="sm"
                >
                  <Play className="w-4 h-4 mr-2" />
                  Start Navigation
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Route Information */}
      {routeInfo && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5" />
              Route Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-zimbabwe-gold-600">
                  {(routeInfo.distance / 1000).toFixed(1)} km
                </div>
                <div className="text-sm text-gray-600">Distance</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-zimbabwe-gold-600">
                  {Math.round(routeInfo.duration / 60)} min
                </div>
                <div className="text-sm text-gray-600">Estimated Time</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {routeInfo.issuesOnRoute.length}
                </div>
                <div className="text-sm text-gray-600">Issues on Route</div>
              </div>
            </div>

            {routeInfo.issuesOnRoute.length > 0 && (
              <div>
                <h4 className="font-semibold mb-2">Problems Along Your Route:</h4>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {routeInfo.issuesOnRoute.map((issue) => (
                    <div key={issue.id} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                      <Badge className={getSeverityColor(issue.severity) + ' text-white text-xs'}>
                        {issue.severity.toUpperCase()}
                      </Badge>
                      <span className="text-sm font-medium">{issue.type.toUpperCase()}</span>
                      <span className="text-sm text-gray-600">{issue.location}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Map */}
      <div style={{ height, width: '100%' }}>
        <MapContainer
          center={center}
          zoom={zoom}
          style={{ height: '100%', width: '100%' }}
          className="rounded-lg"
        >
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />

          {startPoint && endPoint && (
            <RoutingControl
              start={startPoint}
              end={endPoint}
              onRouteFound={handleRouteFound}
              issues={issues}
            />
          )}

          {/* Display issues as markers */}
          {issues.map((issue) => (
            <Marker
              key={issue.id}
              position={issue.coordinates}
              icon={L.divIcon({
                html: `
                  <div style="background: ${getSeverityColor(issue.severity).replace('bg-', '#')}; color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 12px; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);">
                    !
                  </div>
                `,
                className: 'custom-issue-marker',
                iconSize: [20, 20],
                iconAnchor: [10, 10]
              })}
            >
              <Popup>
                <div className="p-2">
                  <h3 className="font-semibold text-sm mb-1">{issue.location}</h3>
                  <p className="text-xs text-gray-600 mb-1">{issue.description}</p>
                  <Badge className={getSeverityColor(issue.severity) + ' text-white text-xs'}>
                    {issue.severity.toUpperCase()}
                  </Badge>
                </div>
              </Popup>
            </Marker>
          ))}
        </MapContainer>
      </div>

      {/* Navigation Mode Overlay */}
      {navigationOverlay}
    </div>
  );
};

export default RouteMap;
