
import { AlertTriangle, Shield, CheckCircle } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface FraudDetectionMeterProps {
  fraudProbability: number;
  riskFactors: string[];
  isAnalyzing?: boolean;
}

const FraudDetectionMeter = ({ 
  fraudProbability, 
  riskFactors,
  isAnalyzing = false 
}: FraudDetectionMeterProps) => {
  const getRiskLevel = (probability: number) => {
    if (probability >= 70) return { level: 'HIGH', color: 'bg-red-500', textColor: 'text-red-700' };
    if (probability >= 30) return { level: 'MEDIUM', color: 'bg-yellow-500', textColor: 'text-yellow-700' };
    return { level: 'LOW', color: 'bg-green-500', textColor: 'text-green-700' };
  };

  const risk = getRiskLevel(fraudProbability);

  if (isAnalyzing) {
    return (
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center animate-pulse">
              <Shield className="w-4 h-4 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-blue-700">RoadGuard AI Analyzing</h3>
              <p className="text-sm text-blue-600">Scanning location and behavioral patterns...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`border-2 ${risk.level === 'HIGH' ? 'border-red-200 bg-red-50' : 
                                 risk.level === 'MEDIUM' ? 'border-yellow-200 bg-yellow-50' : 
                                 'border-green-200 bg-green-50'}`}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Shield className="w-5 h-5 text-road-blue-500" />
            <span className="font-semibold">Fraud Detection</span>
          </div>
          <Badge 
            variant="secondary" 
            className={`${risk.color} text-white font-bold`}
          >
            {risk.level} RISK
          </Badge>
        </div>

        {/* Risk Meter */}
        <div className="mb-3">
          <div className="flex justify-between text-sm mb-1">
            <span>Risk Probability</span>
            <span className={`font-bold ${risk.textColor}`}>{fraudProbability}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-500 ${risk.color}`}
              style={{ width: `${fraudProbability}%` }}
            />
          </div>
        </div>

        {/* Risk Factors */}
        {riskFactors.length > 0 && (
          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-700">Risk Factors Detected:</p>
            <div className="space-y-1">
              {riskFactors.map((factor, index) => (
                <div key={index} className="flex items-center gap-2 text-sm">
                  <AlertTriangle className="w-3 h-3 text-orange-500" />
                  <span className="text-gray-600">{factor}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {fraudProbability < 30 && (
          <div className="flex items-center gap-2 mt-3 text-sm text-green-700">
            <CheckCircle className="w-4 h-4" />
            <span>Request appears legitimate - proceeding with helper matching</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default FraudDetectionMeter;
