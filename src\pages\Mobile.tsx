import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import InteractiveMap, { RoadIssue } from "@/components/InteractiveMap";
import { MapPin, Plus, Filter, AlertTriangle, Construction, Droplets, Car, Menu, Settings, User, Bell, Shield, Camera, Navigation } from "lucide-react";

const Mobile = () => {
  const navigate = useNavigate();
  const [selectedFilter, setSelectedFilter] = useState("all");

  const issueTypes = [
    { id: "pothole", label: "Pothole", icon: AlertTriangle, color: "bg-red-500", count: 12 },
    { id: "flood", label: "Flooding", icon: Droplets, color: "bg-blue-500", count: 5 },
    { id: "obstruction", label: "Obstruction", icon: Car, color: "bg-orange-500", count: 8 },
    { id: "construction", label: "Construction", icon: Construction, color: "bg-yellow-500", count: 3 },
    { id: "police", label: "Police", icon: Shield, color: "bg-blue-600", count: 6 },
    { id: "camera", label: "Cameras", icon: Camera, color: "bg-purple-500", count: 4 }
  ];

  const nearbyIssues = [
    {
      id: 1,
      type: "pothole",
      location: "Harare CBD, 2nd Street",
      distance: "0.2 km",
      severity: "high",
      votes: 15,
      time: "2 hours ago",
      verified: true
    },
    {
      id: 2,
      type: "flood",
      location: "Chitungwiza Road",
      distance: "1.5 km",
      severity: "medium",
      votes: 8,
      time: "4 hours ago",
      verified: false
    },
    {
      id: 3,
      type: "obstruction",
      location: "Borrowdale Road",
      distance: "2.1 km",
      severity: "low",
      votes: 3,
      time: "6 hours ago",
      verified: true
    }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "high": return "bg-red-500";
      case "medium": return "bg-orange-500";
      case "low": return "bg-green-500";
      default: return "bg-gray-500";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-road-charcoal-800 to-road-charcoal-900 text-white p-4 pb-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-road-yellow-500 rounded-xl flex items-center justify-center">
              <MapPin className="w-6 h-6 text-road-charcoal-900" />
            </div>
            <div>
              <h1 className="text-xl font-bold">SmartRoadPulse</h1>
              <p className="text-road-yellow-300 text-sm">Harare, Zimbabwe</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" className="text-white">
              <Bell className="w-5 h-5" />
            </Button>
            <Button variant="ghost" size="sm" className="text-white">
              <Menu className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-3 gap-3 mb-4">
          <Card className="bg-white/10 backdrop-blur-sm border-0">
            <CardContent className="p-3 text-center">
              <div className="text-2xl font-bold text-road-yellow-400">28</div>
              <div className="text-xs text-gray-300">Active Issues</div>
            </CardContent>
          </Card>
          <Card className="bg-white/10 backdrop-blur-sm border-0">
            <CardContent className="p-3 text-center">
              <div className="text-2xl font-bold text-road-blue-400">5.2km</div>
              <div className="text-xs text-gray-300">Coverage Area</div>
            </CardContent>
          </Card>
          <Card className="bg-white/10 backdrop-blur-sm border-0">
            <CardContent className="p-3 text-center">
              <div className="text-2xl font-bold text-green-400">92%</div>
              <div className="text-xs text-gray-300">Road Safety</div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Emergency SOS Section */}
      <div className="px-4 py-4 bg-red-50 border-b border-red-100">
        <Card className="border-red-200 bg-white">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
                  <Shield className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-red-700">RoadGuard Assist</h3>
                  <p className="text-sm text-red-600">24/7 Emergency Response</p>
                </div>
              </div>
              <Button
                onClick={() => navigate("/sos")}
                className="bg-red-500 hover:bg-red-600 text-white font-semibold"
              >
                Emergency SOS
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="p-4 bg-white shadow-sm">
        <div className="flex gap-3 mb-4">
          <Input placeholder="Search location..." className="flex-1" />
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4" />
          </Button>
        </div>

        <div className="flex gap-2 overflow-x-auto pb-2">
          <Button
            variant={selectedFilter === "all" ? "default" : "outline"}
            size="sm"
            className={selectedFilter === "all" ? "bg-road-yellow-500 text-road-charcoal-900" : ""}
            onClick={() => setSelectedFilter("all")}
          >
            All Issues
          </Button>
          {issueTypes.map((type) => (
            <Button
              key={type.id}
              variant={selectedFilter === type.id ? "default" : "outline"}
              size="sm"
              className={`flex items-center gap-1 whitespace-nowrap ${selectedFilter === type.id ? "bg-road-blue-500 text-white" : ""}`}
              onClick={() => setSelectedFilter(type.id)}
            >
              <type.icon className="w-3 h-3" />
              {type.label}
              <Badge variant="secondary" className="text-xs">
                {type.count}
              </Badge>
            </Button>
          ))}
        </div>
      </div>

      {/* Interactive Map */}
      <div className="mx-4 mb-4">
        <Card className="overflow-hidden">
          <InteractiveMap
            height="250px"
            center={[-17.8292, 31.0522]}
            zoom={14}
            showControls={false}
            onIssueClick={(issue: RoadIssue) => {
              console.log('Mobile map issue clicked:', issue);
              // You can add mobile-specific functionality here
            }}
          />
        </Card>
      </div>

      {/* Quick Action Buttons */}
      <div className="px-4 mb-6 space-y-3">
        <Button
          className="w-full bg-zimbabwe-gold-500 hover:bg-zimbabwe-gold-600 text-white font-semibold py-4 text-lg"
          onClick={() => navigate("/route-planning")}
        >
          <Navigation className="w-6 h-6 mr-2" />
          Plan Safe Route
        </Button>
        <Button
          variant="outline"
          className="w-full border-zimbabwe-gold-500 text-zimbabwe-gold-700 hover:bg-zimbabwe-gold-50 font-semibold py-4 text-lg"
          onClick={() => navigate("/report")}
        >
          <Plus className="w-6 h-6 mr-2" />
          Report a Problem
        </Button>
      </div>

      {/* Nearby Issues Feed */}
      <div className="px-4 pb-20">
        <h2 className="text-xl font-bold text-road-charcoal-900 mb-4">Nearby Issues</h2>
        <div className="space-y-3">
          {nearbyIssues.map((issue) => {
            const issueType = issueTypes.find(type => type.id === issue.type);
            return (
              <Card key={issue.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <div className={`w-10 h-10 ${issueType?.color} rounded-lg flex items-center justify-center`}>
                      {issueType && <issueType.icon className="w-5 h-5 text-white" />}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-semibold text-road-charcoal-900">{issue.location}</h3>
                        {issue.verified && (
                          <Badge variant="secondary" className="text-xs bg-green-100 text-green-700">
                            Verified
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                        <span className="flex items-center gap-1">
                          <MapPin className="w-3 h-3" />
                          {issue.distance}
                        </span>
                        <span>{issue.time}</span>
                        <div className={`w-2 h-2 rounded-full ${getSeverityColor(issue.severity)}`}></div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">{issue.votes} people affected</span>
                        <Button variant="ghost" size="sm" className="text-road-blue-600">
                          View Details
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
        <div className="flex justify-around">
          <Button variant="ghost" className="flex flex-col items-center gap-1 text-road-blue-600" onClick={() => navigate("/map")}>
            <MapPin className="w-5 h-5" />
            <span className="text-xs">Map</span>
          </Button>
          <Button variant="ghost" className="flex flex-col items-center gap-1" onClick={() => navigate("/report")}>
            <Plus className="w-5 h-5" />
            <span className="text-xs">Report</span>
          </Button>
          <Button variant="ghost" className="flex flex-col items-center gap-1" onClick={() => navigate("/sos")}>
            <Shield className="w-5 h-5" />
            <span className="text-xs">SOS</span>
          </Button>
          <Button variant="ghost" className="flex flex-col items-center gap-1">
            <User className="w-5 h-5" />
            <span className="text-xs">Profile</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Mobile;
