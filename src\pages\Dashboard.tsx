
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import InteractiveMap, { RoadIssue } from "@/components/InteractiveMap";
import {
  MapPin,
  BarChart3,
  AlertTriangle,
  TrendingUp,
  Users,
  Clock,
  Filter,
  Download,
  Search,
  Settings,
  Bell,
  Navigation
} from "lucide-react";

const Dashboard = () => {
  const navigate = useNavigate();
  const [selectedFilter, setSelectedFilter] = useState("all");

  const stats = [
    {
      title: "Total Reports",
      value: "1,247",
      change: "+12%",
      trend: "up",
      icon: AlertTriangle,
      color: "text-red-600"
    },
    {
      title: "Resolved Issues",
      value: "892",
      change: "+8%",
      trend: "up",
      icon: TrendingUp,
      color: "text-green-600"
    },
    {
      title: "Active Users",
      value: "3,456",
      change: "+23%",
      trend: "up",
      icon: Users,
      color: "text-blue-600"
    },
    {
      title: "Avg Response Time",
      value: "2.4h",
      change: "-15%",
      trend: "down",
      icon: Clock,
      color: "text-yellow-600"
    }
  ];

  const recentReports = [
    {
      id: 1,
      type: "Pothole",
      location: "Harare CBD, 2nd Street",
      severity: "High",
      status: "Under Review",
      time: "2 hours ago",
      reporter: "John M.",
      votes: 15
    },
    {
      id: 2,
      type: "Flooding",
      location: "Chitungwiza Road",
      severity: "Medium",
      status: "In Progress",
      time: "4 hours ago",
      reporter: "Sarah K.",
      votes: 8
    },
    {
      id: 3,
      type: "Road Obstruction",
      location: "Borrowdale Road",
      severity: "Low",
      status: "Resolved",
      time: "6 hours ago",
      reporter: "David L.",
      votes: 3
    }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case "high": return "bg-red-100 text-red-800";
      case "medium": return "bg-yellow-100 text-yellow-800";
      case "low": return "bg-green-100 text-green-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "under review": return "bg-blue-100 text-blue-800";
      case "in progress": return "bg-yellow-100 text-yellow-800";
      case "resolved": return "bg-green-100 text-green-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-road-charcoal-800 to-road-charcoal-900 text-white">
        <div className="container mx-auto px-6 py-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-road-yellow-500 rounded-2xl flex items-center justify-center">
                <BarChart3 className="w-7 h-7 text-road-charcoal-900" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">Authority Dashboard</h1>
                <p className="text-road-yellow-300">Real-time infrastructure monitoring</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Badge className="bg-green-500 text-white">
                <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse-yellow"></div>
                Live
              </Badge>
              <Button variant="ghost" className="text-white">
                <Bell className="w-5 h-5" />
              </Button>
              <Button variant="ghost" className="text-white">
                <Settings className="w-5 h-5" />
              </Button>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="flex gap-3">
            <Button
              className="bg-road-yellow-500 hover:bg-road-yellow-600 text-road-charcoal-900"
              onClick={() => navigate("/analytics")}
            >
              <BarChart3 className="w-4 h-4 mr-2" />
              View Analytics
            </Button>
            <Button variant="outline" className="border-white text-white hover:bg-white hover:text-road-charcoal-900">
              <Download className="w-4 h-4 mr-2" />
              Export Data
            </Button>
            <Button variant="outline" className="border-white text-white hover:bg-white hover:text-road-charcoal-900">
              <MapPin className="w-4 h-4 mr-2" />
              Map View
            </Button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-3xl font-bold text-road-charcoal-900">{stat.value}</p>
                    <p className={`text-sm ${stat.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                      {stat.change} from last month
                    </p>
                  </div>
                  <div className={`p-3 rounded-lg bg-gray-100`}>
                    <stat.icon className={`w-6 h-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid grid-cols-4 w-full max-w-md">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
            <TabsTrigger value="map">Map</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid lg:grid-cols-2 gap-6">
              {/* Interactive Map */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="w-5 h-5" />
                    Live Issue Heatmap
                  </CardTitle>
                  <CardDescription>
                    Real-time visualization of reported road issues across Zimbabwe
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <InteractiveMap
                    height="320px"
                    center={[-17.8292, 31.0522]}
                    zoom={13}
                    showControls={true}
                    onIssueClick={(issue: RoadIssue) => {
                      console.log('Issue clicked:', issue);
                      // You can add more functionality here like opening a modal
                    }}
                  />
                  <div className="mt-4 flex gap-2 justify-center">
                    <Button
                      variant="outline"
                      onClick={() => navigate("/map")}
                      className="bg-road-blue-50 hover:bg-road-blue-100 text-road-blue-700 border-road-blue-200"
                    >
                      <MapPin className="w-4 h-4 mr-2" />
                      Full Map
                    </Button>
                    <Button
                      onClick={() => navigate("/route-planning")}
                      className="bg-zimbabwe-gold-500 hover:bg-zimbabwe-gold-600 text-white"
                    >
                      <Navigation className="w-4 h-4 mr-2" />
                      Plan Route
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="reports" className="space-y-6">
            {/* Filters and Search */}
            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <Input
                      placeholder="Search reports by location, type, or reporter..."
                      className="w-full"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline">
                      <Filter className="w-4 h-4 mr-2" />
                      Filter
                    </Button>
                    <Button variant="outline">
                      <Download className="w-4 h-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Reports Table */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Reports</CardTitle>
                <CardDescription>Latest road issue reports from citizens</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentReports.map((report) => (
                    <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-road-blue-100 rounded-lg flex items-center justify-center">
                          <AlertTriangle className="w-6 h-6 text-road-blue-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-road-charcoal-900">{report.type}</h3>
                          <p className="text-sm text-gray-600">{report.location}</p>
                          <p className="text-xs text-gray-500">
                            Reported by {report.reporter} • {report.time} • {report.votes} votes
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Badge className={getSeverityColor(report.severity)}>
                          {report.severity}
                        </Badge>
                        <Badge className={getStatusColor(report.status)}>
                          {report.status}
                        </Badge>
                        <Button variant="outline" size="sm">
                          View Details
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="map" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Full Screen Map View</CardTitle>
                <CardDescription>Comprehensive map interface with advanced filtering and analysis tools</CardDescription>
              </CardHeader>
              <CardContent>
                <InteractiveMap
                  height="500px"
                  center={[-17.8292, 31.0522]}
                  zoom={12}
                  showControls={true}
                  onIssueClick={(issue: RoadIssue) => {
                    console.log('Full map issue clicked:', issue);
                    // You can add more functionality here like opening a detailed modal
                  }}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>System Configuration</CardTitle>
                  <CardDescription>Manage dashboard settings and preferences</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button variant="outline" className="w-full justify-start">
                    <Users className="w-4 h-4 mr-2" />
                    User Management
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Bell className="w-4 h-4 mr-2" />
                    Notification Settings
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Download className="w-4 h-4 mr-2" />
                    Data Export Options
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>API Management</CardTitle>
                  <CardDescription>Integration and third-party access controls</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button variant="outline" className="w-full justify-start">
                    Generate API Key
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    View Documentation
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    Access Logs
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Dashboard;
