// Routing service configuration for SmartRoadPulse
// This file contains configuration for different routing service providers

export interface RoutingServiceConfig {
  name: string;
  serviceUrl: string;
  profile: string;
  timeout: number;
  isProduction: boolean;
  description: string;
  usagePolicy?: string;
}

// Available routing services
export const ROUTING_SERVICES: Record<string, RoutingServiceConfig> = {
  // OpenStreetMap Germany - More reliable for production
  OSM_GERMANY: {
    name: 'OpenStreetMap Germany',
    serviceUrl: 'https://routing.openstreetmap.de',
    profile: 'driving',
    timeout: 30 * 1000,
    isProduction: true,
    description: 'Reliable routing service hosted by OpenStreetMap Germany',
    usagePolicy: 'https://www.openstreetmap.de/germanstyle.html'
  },

  // GraphHopper - Good alternative
  GRAPHHOPPER: {
    name: 'GraphHopper',
    serviceUrl: 'https://graphhopper.com/api/1/route',
    profile: 'car',
    timeout: 30 * 1000,
    isProduction: true,
    description: 'GraphHopper routing service (requires API key for production)',
    usagePolicy: 'https://www.graphhopper.com/api/'
  },

  // OSRM Demo - Only for development/testing
  OSRM_DEMO: {
    name: 'OSRM Demo Server',
    serviceUrl: 'https://router.project-osrm.org',
    profile: 'driving',
    timeout: 15 * 1000,
    isProduction: false,
    description: 'OSRM demo server - NOT FOR PRODUCTION USE',
    usagePolicy: 'https://github.com/Project-OSRM/osrm-backend/wiki/Api-usage-policy'
  },

  // Local OSRM - For self-hosted instances
  LOCAL_OSRM: {
    name: 'Local OSRM',
    serviceUrl: 'http://localhost:5000/route/v1',
    profile: 'driving',
    timeout: 30 * 1000,
    isProduction: true,
    description: 'Self-hosted OSRM instance'
  }
};

// Default routing service for production
export const DEFAULT_ROUTING_SERVICE = ROUTING_SERVICES.OSM_GERMANY;

// Development routing service
export const DEV_ROUTING_SERVICE = ROUTING_SERVICES.OSRM_DEMO;

// Get current routing service based on environment
export const getCurrentRoutingService = (): RoutingServiceConfig => {
  const isDevelopment = import.meta.env.DEV; // Vite's built-in development flag

  // In development, use the more reliable OSRM demo service
  if (isDevelopment) {
    console.warn('🚧 Using development routing service. Switch to production service for live deployment.');
    return DEV_ROUTING_SERVICE; // Use OSRM demo for better reliability in development
  }

  return DEFAULT_ROUTING_SERVICE;
};

// Routing service recommendations for Zimbabwe
export const ZIMBABWE_ROUTING_RECOMMENDATIONS = {
  primary: ROUTING_SERVICES.OSM_GERMANY,
  fallback: ROUTING_SERVICES.LOCAL_OSRM,
  notes: [
    'For production deployment in Zimbabwe, consider setting up a local OSRM server',
    'OpenStreetMap Germany provides good coverage for African countries',
    'Always have a fallback to straight-line routing for remote areas',
    'Consider caching routes for frequently used paths'
  ]
};

// Production deployment checklist
export const PRODUCTION_CHECKLIST = [
  '✅ Switch from demo OSRM to production routing service',
  '✅ Set up local OSRM server for better performance',
  '✅ Implement route caching for popular destinations',
  '✅ Add offline fallback routing',
  '✅ Monitor routing service uptime and performance',
  '✅ Implement rate limiting and error handling',
  '✅ Add Zimbabwe-specific road data if available'
];

// Error messages for routing failures
export const ROUTING_ERROR_MESSAGES = {
  SERVICE_UNAVAILABLE: 'Routing service is temporarily unavailable. Using direct route.',
  TIMEOUT: 'Route calculation timed out. Using estimated route.',
  NO_ROUTE_FOUND: 'No route found between these points. Using direct path.',
  NETWORK_ERROR: 'Network error occurred. Check your internet connection.',
  RATE_LIMITED: 'Too many requests. Please wait a moment and try again.'
};

// Fallback routing configuration
export const FALLBACK_CONFIG = {
  enableStraightLineRouting: true,
  averageSpeed: 50, // km/h for time estimation
  bufferDistance: 0.005, // degrees for issue detection
  maxRetries: 3,
  retryDelay: 2000 // milliseconds
};
