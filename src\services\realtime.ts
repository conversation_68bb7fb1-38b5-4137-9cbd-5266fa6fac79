/**
 * Real-time Service for RoadPulse
 * Handles WebSocket connections for live updates
 */

import { io, Socket } from 'socket.io-client';
import { RoadIssue } from '../components/InteractiveMap';
import { S<PERSON><PERSON><PERSON>t, SOSHelper } from '../types/sos';

// Real-time event types
export interface RealtimeEvents {
  // SOS Events
  'sos:created': SOSAlert;
  'sos:helper_responded': {
    sosId: string;
    helper: SOSHelper;
    estimatedArrival: number;
  };
  'sos:status_updated': {
    sosId: string;
    status: SOSAlert['status'];
    updatedBy: string;
  };
  'sos:completed': {
    sosId: string;
    completedAt: string;
    rating?: number;
  };
  
  // Location Events
  'location:helper_nearby': {
    helper: SOSHelper;
    distance: number;
  };
  'location:updated': {
    userId: string;
    coordinates: [number, number];
    heading?: number;
    speed?: number;
  };
  
  // Issue Events
  'issue:created': RoadIssue;
  'issue:verified': {
    issueId: string;
    verifiedBy: string;
    verificationType: 'confirm' | 'dispute';
  };
  'issue:resolved': {
    issueId: string;
    resolvedBy: string;
    resolvedAt: string;
  };
  
  // Navigation Events
  'navigation:traffic_update': {
    location: [number, number];
    severity: 'light' | 'moderate' | 'heavy';
    description: string;
  };
  'navigation:police_spotted': {
    location: [number, number];
    type: 'checkpoint' | 'patrol' | 'traffic';
    reportedBy: string;
    confidence: number;
  };
  'navigation:camera_ahead': {
    location: [number, number];
    type: 'speed' | 'traffic' | 'security';
    distance: number;
  };
}

export type EventCallback<T = any> = (data: T) => void;

class RealtimeService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;
  private eventListeners: Map<string, EventCallback[]> = new Map();

  /**
   * Connect to the WebSocket server
   */
  connect(userId?: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        // Wait for existing connection attempt
        const checkConnection = () => {
          if (this.socket?.connected) {
            resolve();
          } else if (!this.isConnecting) {
            reject(new Error('Connection failed'));
          } else {
            setTimeout(checkConnection, 100);
          }
        };
        checkConnection();
        return;
      }

      this.isConnecting = true;
      const token = localStorage.getItem('auth_token');
      
      this.socket = io(import.meta.env.VITE_WS_URL || 'ws://localhost:3001', {
        auth: { token },
        transports: ['websocket', 'polling'],
        timeout: 10000,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay,
      });

      this.socket.on('connect', () => {
        console.log('✅ Connected to real-time service');
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        
        if (userId) {
          this.socket?.emit('join_user_room', userId);
        }
        
        // Re-register all event listeners
        this.reregisterEventListeners();
        
        resolve();
      });

      this.socket.on('connect_error', (error) => {
        console.error('❌ Real-time connection error:', error);
        this.isConnecting = false;
        this.reconnectAttempts++;
        
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          reject(new Error(`Failed to connect after ${this.maxReconnectAttempts} attempts`));
        }
      });

      this.socket.on('disconnect', (reason) => {
        console.log('🔌 Disconnected from real-time service:', reason);
        this.isConnecting = false;
        
        if (reason === 'io server disconnect') {
          // Server disconnected, try to reconnect
          setTimeout(() => this.connect(userId), this.reconnectDelay);
        }
      });

      this.socket.on('reconnect', (attemptNumber) => {
        console.log(`🔄 Reconnected to real-time service (attempt ${attemptNumber})`);
        this.reconnectAttempts = 0;
      });

      this.socket.on('error', (error) => {
        console.error('🚨 Real-time service error:', error);
      });
    });
  }

  /**
   * Disconnect from the WebSocket server
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.eventListeners.clear();
    this.isConnecting = false;
  }

  /**
   * Check if connected
   */
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * Subscribe to real-time events
   */
  on<K extends keyof RealtimeEvents>(
    event: K,
    callback: (data: RealtimeEvents[K]) => void
  ): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    
    this.eventListeners.get(event)!.push(callback);
    
    if (this.socket?.connected) {
      this.socket.on(event, callback);
    }
  }

  /**
   * Unsubscribe from real-time events
   */
  off<K extends keyof RealtimeEvents>(
    event: K,
    callback?: (data: RealtimeEvents[K]) => void
  ): void {
    if (callback) {
      const listeners = this.eventListeners.get(event) || [];
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
      
      if (this.socket?.connected) {
        this.socket.off(event, callback);
      }
    } else {
      // Remove all listeners for this event
      this.eventListeners.delete(event);
      if (this.socket?.connected) {
        this.socket.off(event);
      }
    }
  }

  /**
   * Emit events to the server
   */
  emit(event: string, data?: any): void {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    } else {
      console.warn('Cannot emit event: not connected to real-time service');
    }
  }

  /**
   * Join a specific room (for location-based updates)
   */
  joinLocationRoom(lat: number, lng: number, radius = 5): void {
    this.emit('join_location_room', { lat, lng, radius });
  }

  /**
   * Leave a location room
   */
  leaveLocationRoom(lat: number, lng: number): void {
    this.emit('leave_location_room', { lat, lng });
  }

  /**
   * Start sharing location for helping
   */
  startLocationSharing(location: { lat: number; lng: number; accuracy: number }): void {
    this.emit('start_location_sharing', location);
  }

  /**
   * Stop sharing location
   */
  stopLocationSharing(): void {
    this.emit('stop_location_sharing');
  }

  /**
   * Update current location
   */
  updateLocation(location: {
    lat: number;
    lng: number;
    accuracy: number;
    heading?: number;
    speed?: number;
  }): void {
    this.emit('location_update', location);
  }

  /**
   * Subscribe to SOS-related events
   */
  subscribeToSOSUpdates(callback: (data: any) => void): () => void {
    this.on('sos:created', callback);
    this.on('sos:helper_responded', callback);
    this.on('sos:status_updated', callback);
    this.on('sos:completed', callback);

    // Return unsubscribe function
    return () => {
      this.off('sos:created', callback);
      this.off('sos:helper_responded', callback);
      this.off('sos:status_updated', callback);
      this.off('sos:completed', callback);
    };
  }

  /**
   * Subscribe to navigation-related events
   */
  subscribeToNavigationUpdates(callback: (data: any) => void): () => void {
    this.on('navigation:traffic_update', callback);
    this.on('navigation:police_spotted', callback);
    this.on('navigation:camera_ahead', callback);

    return () => {
      this.off('navigation:traffic_update', callback);
      this.off('navigation:police_spotted', callback);
      this.off('navigation:camera_ahead', callback);
    };
  }

  /**
   * Subscribe to issue-related events
   */
  subscribeToIssueUpdates(callback: (data: any) => void): () => void {
    this.on('issue:created', callback);
    this.on('issue:verified', callback);
    this.on('issue:resolved', callback);

    return () => {
      this.off('issue:created', callback);
      this.off('issue:verified', callback);
      this.off('issue:resolved', callback);
    };
  }

  /**
   * Re-register all event listeners after reconnection
   */
  private reregisterEventListeners(): void {
    this.eventListeners.forEach((callbacks, event) => {
      callbacks.forEach(callback => {
        this.socket?.on(event, callback);
      });
    });
  }

  /**
   * Get connection status
   */
  getStatus(): {
    connected: boolean;
    reconnectAttempts: number;
    isConnecting: boolean;
  } {
    return {
      connected: this.isConnected(),
      reconnectAttempts: this.reconnectAttempts,
      isConnecting: this.isConnecting,
    };
  }
}

// Export singleton instance
export const realtimeService = new RealtimeService();

// Export for testing
export { RealtimeService };
