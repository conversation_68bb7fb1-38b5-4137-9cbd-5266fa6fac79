/**
 * Data Service for RoadPulse
 * Manages the transition from mock data to real API data
 * Provides a unified interface for data access
 */

import { RoadIssue } from '../components/InteractiveMap';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SOSHelper } from '../types/sos';
import { apiService, IssueFilters, CreateIssueRequest, CreateSOSRequest } from './api';
import { realtimeService } from './realtime';

// Configuration for development/production modes
const DATA_CONFIG = {
  useMockData: import.meta.env.VITE_USE_MOCK_DATA === 'true' || !import.meta.env.VITE_API_URL,
  enableRealtime: import.meta.env.VITE_ENABLE_REALTIME !== 'false',
  mockDataDelay: 500, // Simulate network delay in development
};

// Mock data (keeping existing structure for development)
const MOCK_ISSUES: RoadIssue[] = [
  {
    id: '1',
    type: 'pothole',
    location: 'Samora Machel Avenue',
    coordinates: [-17.8292, 31.0522],
    severity: 'high',
    description: 'Large pothole causing traffic delays',
    reportedBy: '<PERSON>',
    reportedAt: '2024-01-15T10:30:00Z',
    status: 'open',
    verified: true
  },
  {
    id: '7',
    type: 'sos',
    location: 'Samora Machel Avenue CBD',
    coordinates: [-17.8292, 31.0522],
    severity: 'critical',
    description: 'Medical emergency - passenger feeling unwell',
    reportedBy: 'Tendai Mukamuri',
    reportedAt: '2024-01-15T18:00:00Z',
    status: 'active',
    verified: true,
    isActive: true,
    sosType: 'medical',
    helpNeeded: 'Passenger feeling unwell, need medical help',
    urgencyLevel: 'critical',
    estimatedHelpers: 5,
    responseTime: 8,
    userProfile: {
      name: 'Tendai Mukamuri',
      phone: '+263 77 123 4567',
      rating: 4.9,
      trustScore: 95,
      profileImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      vehicleInfo: 'Blue Honda Fit (XYZ-789Z)'
    }
  },
  // Add more mock data as needed...
];

const MOCK_SOS_HELPERS: SOSHelper[] = [
  {
    id: "1",
    name: "John Mukamuri",
    trustScore: 95,
    location: { lat: -17.8292, lng: 31.0522 },
    distance: 1.2,
    responseTime: 180,
    rating: 4.8,
    assistanceCount: 23,
    eta: 8,
    badges: [
      {
        type: 'gold_guardian',
        title: 'Gold Guardian',
        description: 'Completed 20+ successful assists',
        earnedDate: new Date('2024-01-15')
      }
    ]
  }
];

class DataService {
  private cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();
  private isInitialized = false;

  /**
   * Initialize the data service
   */
  async initialize(userId?: string): Promise<void> {
    if (this.isInitialized) return;

    console.log(`🔧 Initializing DataService (Mock: ${DATA_CONFIG.useMockData})`);

    if (!DATA_CONFIG.useMockData) {
      try {
        // Test API connection
        await apiService.healthCheck();
        console.log('✅ API connection established');

        // Initialize real-time connection if enabled
        if (DATA_CONFIG.enableRealtime && userId) {
          await realtimeService.connect(userId);
          this.setupRealtimeListeners();
          console.log('✅ Real-time service connected');
        }
      } catch (error) {
        console.warn('⚠️ API connection failed, falling back to mock data:', error);
        DATA_CONFIG.useMockData = true;
      }
    }

    this.isInitialized = true;
  }

  /**
   * Get road issues with optional filters
   */
  async getIssues(filters: IssueFilters = {}): Promise<RoadIssue[]> {
    const cacheKey = `issues_${JSON.stringify(filters)}`;
    
    // Check cache first
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    let issues: RoadIssue[];

    if (DATA_CONFIG.useMockData) {
      // Simulate API delay
      await this.delay(DATA_CONFIG.mockDataDelay);
      issues = this.filterMockIssues(MOCK_ISSUES, filters);
    } else {
      issues = await apiService.getIssues(filters);
    }

    // Cache for 30 seconds
    this.setCache(cacheKey, issues, 30000);
    return issues;
  }

  /**
   * Get nearby issues based on location
   */
  async getNearbyIssues(lat: number, lng: number, radius = 5): Promise<RoadIssue[]> {
    return this.getIssues({ lat, lng, radius });
  }

  /**
   * Create a new road issue
   */
  async createIssue(issueData: CreateIssueRequest): Promise<RoadIssue> {
    if (DATA_CONFIG.useMockData) {
      await this.delay(DATA_CONFIG.mockDataDelay);
      
      const newIssue: RoadIssue = {
        id: Date.now().toString(),
        type: issueData.type,
        location: issueData.location_name,
        coordinates: issueData.coordinates,
        severity: issueData.severity,
        description: issueData.description,
        reportedBy: 'Current User',
        reportedAt: new Date().toISOString(),
        status: 'open',
        verified: false,
        ...(issueData.sos_type && {
          sosType: issueData.sos_type,
          helpNeeded: issueData.help_needed,
          urgencyLevel: issueData.urgency_level,
          isActive: true,
          estimatedHelpers: 0,
          responseTime: 0,
        })
      };

      // Add to mock data
      MOCK_ISSUES.unshift(newIssue);
      this.clearCache('issues_');
      
      return newIssue;
    } else {
      const issue = await apiService.createIssue(issueData);
      this.clearCache('issues_');
      return issue;
    }
  }

  /**
   * Create SOS request
   */
  async createSOS(sosData: CreateSOSRequest): Promise<SOSAlert> {
    if (DATA_CONFIG.useMockData) {
      await this.delay(DATA_CONFIG.mockDataDelay);
      
      const sosAlert: SOSAlert = {
        id: Date.now().toString(),
        userId: 'current_user',
        location: sosData.location,
        timestamp: new Date(),
        status: 'active',
        riskScore: 0.3,
        fraudProbability: 0.1,
        description: sosData.description,
        category: sosData.category,
        helpers: [],
        escalationHistory: []
      };

      return sosAlert;
    } else {
      return apiService.createSOS(sosData);
    }
  }

  /**
   * Get active SOS requests
   */
  async getActiveSOSRequests(lat?: number, lng?: number, radius = 10): Promise<SOSAlert[]> {
    if (DATA_CONFIG.useMockData) {
      await this.delay(DATA_CONFIG.mockDataDelay);
      
      // Filter mock SOS issues
      const sosIssues = MOCK_ISSUES.filter(issue => 
        issue.type === 'sos' && issue.isActive
      );

      return sosIssues.map(issue => ({
        id: issue.id,
        userId: 'mock_user',
        location: {
          lat: issue.coordinates[0],
          lng: issue.coordinates[1],
          accuracy: 10,
          address: issue.location
        },
        timestamp: new Date(issue.reportedAt),
        status: 'active' as const,
        riskScore: 0.3,
        fraudProbability: 0.1,
        description: issue.description,
        category: (issue.sosType || 'other') as SOSAlert['category'],
        helpers: [],
        escalationHistory: []
      }));
    } else {
      return apiService.getActiveSOSRequests(lat, lng, radius);
    }
  }

  /**
   * Get nearby helpers
   */
  async getNearbyHelpers(lat: number, lng: number, radius = 5): Promise<SOSHelper[]> {
    if (DATA_CONFIG.useMockData) {
      await this.delay(DATA_CONFIG.mockDataDelay);
      return MOCK_SOS_HELPERS.filter(helper => 
        helper.distance <= radius
      );
    } else {
      return apiService.getNearbyHelpers(lat, lng, radius);
    }
  }

  /**
   * Update user location
   */
  async updateLocation(location: {
    lat: number;
    lng: number;
    accuracy: number;
    heading?: number;
    speed?: number;
  }): Promise<void> {
    if (DATA_CONFIG.useMockData) {
      // In mock mode, just update real-time service if connected
      if (realtimeService.isConnected()) {
        realtimeService.updateLocation(location);
      }
      return;
    } else {
      await apiService.updateLocation(location);
    }
  }

  /**
   * Verify an issue
   */
  async verifyIssue(issueId: string, verification: {
    type: 'confirm' | 'dispute' | 'update';
    notes?: string;
  }): Promise<void> {
    if (DATA_CONFIG.useMockData) {
      await this.delay(DATA_CONFIG.mockDataDelay);
      
      const issue = MOCK_ISSUES.find(i => i.id === issueId);
      if (issue) {
        issue.verified = verification.type === 'confirm';
      }
      
      this.clearCache('issues_');
      return;
    } else {
      await apiService.verifyIssue(issueId, verification);
      this.clearCache('issues_');
    }
  }

  /**
   * Setup real-time event listeners
   */
  private setupRealtimeListeners(): void {
    // Clear cache when new issues are created
    realtimeService.on('issue:created', () => {
      this.clearCache('issues_');
    });

    realtimeService.on('issue:verified', () => {
      this.clearCache('issues_');
    });

    realtimeService.on('issue:resolved', () => {
      this.clearCache('issues_');
    });
  }

  /**
   * Cache management
   */
  private getFromCache(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  private setCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  private clearCache(keyPrefix: string): void {
    for (const key of this.cache.keys()) {
      if (key.startsWith(keyPrefix)) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Filter mock issues based on filters
   */
  private filterMockIssues(issues: RoadIssue[], filters: IssueFilters): RoadIssue[] {
    let filtered = [...issues];

    if (filters.type && filters.type !== 'all') {
      filtered = filtered.filter(issue => issue.type === filters.type);
    }

    if (filters.severity && filters.severity !== 'all') {
      filtered = filtered.filter(issue => issue.severity === filters.severity);
    }

    if (filters.status && filters.status !== 'all') {
      filtered = filtered.filter(issue => issue.status === filters.status);
    }

    if (filters.lat && filters.lng && filters.radius) {
      filtered = filtered.filter(issue => {
        const distance = this.calculateDistance(
          filters.lat!, filters.lng!,
          issue.coordinates[0], issue.coordinates[1]
        );
        return distance <= filters.radius!;
      });
    }

    if (filters.limit) {
      filtered = filtered.slice(0, filters.limit);
    }

    return filtered;
  }

  /**
   * Calculate distance between two points (Haversine formula)
   */
  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get service status
   */
  getStatus(): {
    useMockData: boolean;
    enableRealtime: boolean;
    isInitialized: boolean;
    realtimeConnected: boolean;
  } {
    return {
      useMockData: DATA_CONFIG.useMockData,
      enableRealtime: DATA_CONFIG.enableRealtime,
      isInitialized: this.isInitialized,
      realtimeConnected: realtimeService.isConnected(),
    };
  }
}

// Export singleton instance
export const dataService = new DataService();

// Export for testing
export { DataService };
