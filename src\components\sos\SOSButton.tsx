
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { AlertTriangle, Shield } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";

interface SOSButtonProps {
  onActivate: () => void;
  className?: string;
}

const SOSButton = ({ onActivate, className }: SOSButtonProps) => {
  const [isActivating, setIsActivating] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const { toast } = useToast();

  const handleSOSPress = () => {
    setIsActivating(true);
    setCountdown(3);
    
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          setIsActivating(false);
          onActivate();
          toast({
            title: "SOS Activated",
            description: "Emergency alert sent. RoadGuard AI is analyzing your request.",
          });
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const cancelSOS = () => {
    setIsActivating(false);
    setCountdown(0);
  };

  if (isActivating) {
    return (
      <Card className="border-red-500 bg-red-50">
        <CardContent className="p-6 text-center">
          <div className="flex flex-col items-center space-y-4">
            <div className="w-20 h-20 bg-red-500 rounded-full flex items-center justify-center animate-pulse">
              <span className="text-white text-2xl font-bold">{countdown}</span>
            </div>
            <div>
              <h3 className="font-semibold text-red-700">Activating Emergency Alert</h3>
              <p className="text-sm text-red-600">Release to cancel</p>
            </div>
            <Button 
              variant="outline" 
              onClick={cancelSOS}
              className="border-red-500 text-red-700"
            >
              Cancel SOS
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <Button
        onClick={handleSOSPress}
        className="w-full h-16 bg-red-500 hover:bg-red-600 text-white text-lg font-semibold"
        size="lg"
      >
        <AlertTriangle className="w-6 h-6 mr-2" />
        Emergency SOS
      </Button>
      
      <div className="flex items-center justify-center gap-2 text-xs text-gray-600">
        <Shield className="w-4 h-4 text-road-blue-500" />
        <span>Protected by RoadGuard AI</span>
        <Badge variant="secondary" className="text-xs">90% Fraud Detection</Badge>
      </div>
    </div>
  );
};

export default SOSButton;
