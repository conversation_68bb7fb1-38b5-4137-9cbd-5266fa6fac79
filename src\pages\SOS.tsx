import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, MapPin, Phone, MessageSquare } from "lucide-react";
import { useNavigate } from "react-router-dom";
import SOSButton from "@/components/sos/SOSButton";
import FraudDetectionMeter from "@/components/sos/FraudDetectionMeter";
import HelperMatchingScreen from "@/components/sos/HelperMatchingScreen";
import TrustScoreDisplay from "@/components/sos/TrustScoreDisplay";
import RoadblockDetector from "@/components/sos/RoadblockDetector";
import { S<PERSON><PERSON><PERSON>t, SOSHelper, RoadblockInfo } from "@/types/sos";
import { useToast } from "@/hooks/use-toast";

const SOS = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [sosActive, setSosActive] = useState(false);
  const [currentStep, setCurrentStep] = useState<'initial' | 'analyzing' | 'matching' | 'connected'>('initial');
  const [fraudProbability, setFraudProbability] = useState(0);
  const [riskFactors, setRiskFactors] = useState<string[]>([]);
  const [helpers, setHelpers] = useState<SOSHelper[]>([]);
  const [countdown, setCountdown] = useState(900); // 15 minutes
  const [roadblockInfo, setRoadblockInfo] = useState<RoadblockInfo | null>(null);

  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (sosActive && countdown > 0) {
      intervalId = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    }

    return () => clearInterval(intervalId);
  }, [sosActive, countdown]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleSOSActivation = () => {
    setSosActive(true);
    setCurrentStep('analyzing');
    
    // Simulate AI fraud detection
    setTimeout(() => {
      setFraudProbability(15);
      setRiskFactors([
        "New account (created 2 days ago)",
        "Location matches known safe area"
      ]);
      setCurrentStep('matching');
      
      // Mock helper data with roadblock reporter badge
      const mockHelpers: SOSHelper[] = [
        {
          id: "1",
          name: "John Mukamuri",
          trustScore: 95,
          location: { lat: -17.8292, lng: 31.0522 },
          distance: 1.2,
          responseTime: 180,
          rating: 4.8,
          assistanceCount: 23,
          eta: 8,
          badges: [
            {
              type: 'gold_guardian',
              title: 'Gold Guardian',
              description: 'Completed 20+ successful assists',
              earnedDate: new Date('2024-01-15')
            },
            {
              type: 'roadblock_reporter',
              title: 'Traffic Intel',
              description: 'Accurate roadblock reports',
              earnedDate: new Date('2024-02-20'),
              count: 15
            }
          ]
        },
        {
          id: "2", 
          name: "Mary Chisango",
          trustScore: 88,
          location: { lat: -17.8352, lng: 31.0422 },
          distance: 2.1,
          responseTime: 300,
          rating: 4.6,
          assistanceCount: 18,
          eta: 12,
          badges: [
            {
              type: 'verified_helper',
              title: 'Verified Helper',
              description: 'Identity verified by RoadGuard',
              earnedDate: new Date('2024-03-01')
            },
            {
              type: 'rapid_responder',
              title: 'Rapid Responder',
              description: 'Average response time under 5 minutes',
              earnedDate: new Date('2024-03-15')
            }
          ]
        }
      ];
      setHelpers(mockHelpers);
    }, 3000);
  };

  const handleHelperSelection = (helper: SOSHelper) => {
    toast({
      title: "Helper Contacted",
      description: `${helper.name} has been notified and is en route to your location.`,
    });
    setCurrentStep('connected');
  };

  const handleRoadblockDetected = (roadblock: RoadblockInfo) => {
    setRoadblockInfo(roadblock);
    toast({
      title: "Roadblock Alert Shared",
      description: "Nearby helpers have been notified about the roadblock situation.",
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-road-charcoal-800 to-road-charcoal-900 text-white p-4">
        <div className="flex items-center gap-3 mb-4">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => navigate(-1)}
            className="text-white hover:bg-white/10"
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div>
            <h1 className="text-xl font-bold">RoadGuard Assist</h1>
            <p className="text-road-yellow-300 text-sm">AI-Powered Emergency Response</p>
          </div>
        </div>

        {sosActive && (
          <div className="bg-red-500/20 border border-red-500 rounded-lg p-3">
            <p className="text-red-200 text-sm font-medium">
              🚨 EMERGENCY ACTIVE - Help is on the way
            </p>
          </div>
        )}
      </div>

      <div className="p-4 space-y-6">
        {!sosActive ? (
          <>
            {/* Initial SOS Interface */}
            <SOSButton onActivate={handleSOSActivation} />
            
            {/* Roadblock Detection */}
            <RoadblockDetector 
              location={{ lat: -17.8292, lng: 31.0522 }}
              onRoadblockDetected={handleRoadblockDetected}
            />

            {/* Safety Tips */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Emergency Guidelines</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-road-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">1</div>
                  <p className="text-sm">Only use SOS for genuine emergencies</p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-road-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">2</div>
                  <p className="text-sm">Stay in your vehicle unless it's unsafe</p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-road-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">3</div>
                  <p className="text-sm">Share your location with trusted contacts</p>
                </div>
              </CardContent>
            </Card>
          </>
        ) : (
          <>
            {/* Active SOS Flow */}
            {currentStep === 'analyzing' && (
              <FraudDetectionMeter 
                fraudProbability={fraudProbability}
                riskFactors={riskFactors}
                isAnalyzing={true}
              />
            )}

            {currentStep === 'matching' && (
              <>
                <FraudDetectionMeter 
                  fraudProbability={fraudProbability}
                  riskFactors={riskFactors}
                />
                
                {roadblockInfo && (
                  <Card className="border-orange-200 bg-orange-50">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <MapPin className="w-4 h-4 text-orange-600" />
                        <span className="font-semibold text-orange-700">Roadblock Alert</span>
                      </div>
                      <p className="text-sm text-orange-600">
                        {roadblockInfo.type} roadblock detected - Helpers have been informed
                      </p>
                    </CardContent>
                  </Card>
                )}
                
                <HelperMatchingScreen 
                  helpers={helpers}
                  onSelectHelper={handleHelperSelection}
                  countdown={countdown}
                />
              </>
            )}

            {currentStep === 'connected' && (
              <Card className="border-green-200 bg-green-50">
                <CardContent className="p-6 text-center">
                  <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Phone className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-green-700 mb-2">Helper En Route</h3>
                  <p className="text-green-600 mb-4">
                    John Mukamuri is coming to assist you. ETA: 8 minutes
                  </p>
                  <div className="flex gap-2 justify-center">
                    <Button variant="outline" size="sm">
                      <Phone className="w-4 h-4 mr-1" />
                      Call Helper
                    </Button>
                    <Button variant="outline" size="sm">
                      <MessageSquare className="w-4 h-4 mr-1" />
                      Message
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default SOS;
