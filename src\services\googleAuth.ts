/**
 * Google OAuth Authentication Service
 * Handles Google Sign-In integration for RoadPulse
 */

import { apiService } from './api';

// Google OAuth Configuration
const GOOGLE_CONFIG = {
  clientId: import.meta.env.VITE_GOOGLE_CLIENT_ID || '',
  redirectUri: import.meta.env.VITE_GOOGLE_REDIRECT_URI || `${window.location.origin}/auth/callback`,
  scope: 'openid email profile',
  responseType: 'code',
  accessType: 'offline',
  prompt: 'consent'
};

// Google User Profile Interface
export interface GoogleUser {
  id: string;
  email: string;
  name: string;
  given_name: string;
  family_name: string;
  picture: string;
  verified_email: boolean;
  locale?: string;
}

// Google Auth Response Interface
export interface GoogleAuthResponse {
  access_token: string;
  id_token: string;
  scope: string;
  token_type: string;
  expires_in: number;
}

// RoadPulse User Registration Data
export interface UserRegistrationData {
  email: string;
  name: string;
  firstName: string;
  lastName: string;
  profileImage?: string;
  provider: 'google' | 'email';
  providerId?: string;
  phone?: string;
  location?: {
    country: string;
    city?: string;
  };
}

class GoogleAuthService {
  private isInitialized = false;
  private gapi: any = null;
  private auth2: any = null;

  /**
   * Initialize Google API
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    if (!GOOGLE_CONFIG.clientId) {
      throw new Error('Google Client ID not configured. Please set VITE_GOOGLE_CLIENT_ID');
    }

    try {
      // Load Google API script
      await this.loadGoogleAPI();
      
      // Initialize gapi
      await new Promise<void>((resolve, reject) => {
        window.gapi.load('auth2', {
          callback: () => {
            window.gapi.auth2.init({
              client_id: GOOGLE_CONFIG.clientId,
              scope: GOOGLE_CONFIG.scope,
              fetch_basic_profile: true,
              ux_mode: 'popup'
            }).then(() => {
              this.gapi = window.gapi;
              this.auth2 = window.gapi.auth2.getAuthInstance();
              this.isInitialized = true;
              resolve();
            }).catch(reject);
          },
          onerror: reject
        });
      });

      console.log('✅ Google Auth initialized successfully');
    } catch (error: any) {
      console.error('❌ Failed to initialize Google Auth:', error);

      // Handle origin not registered error gracefully
      if (error?.details?.includes('Not a valid origin') || error?.error === 'idpiframe_initialization_failed') {
        console.warn('🔧 Google OAuth origin not registered. Please add http://localhost:8083 to your Google OAuth client.');
        console.warn('📋 Instructions: https://console.cloud.google.com/apis/credentials');
        console.warn('🔄 For now, email authentication is available.');

        // Don't throw error, just mark as not initialized
        this.isInitialized = false;
        return;
      }

      throw error;
    }
  }

  /**
   * Load Google API script dynamically
   */
  private loadGoogleAPI(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Check if already loaded
      if (window.gapi) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://apis.google.com/js/api.js';
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load Google API'));
      document.head.appendChild(script);
    });
  }

  /**
   * Sign in with Google
   */
  async signIn(): Promise<{ user: GoogleUser; isNewUser: boolean }> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const authResult = await this.auth2.signIn({
        scope: GOOGLE_CONFIG.scope,
        prompt: 'select_account'
      });

      const profile = authResult.getBasicProfile();
      const idToken = authResult.getAuthResponse().id_token;

      const googleUser: GoogleUser = {
        id: profile.getId(),
        email: profile.getEmail(),
        name: profile.getName(),
        given_name: profile.getGivenName(),
        family_name: profile.getFamilyName(),
        picture: profile.getImageUrl(),
        verified_email: true
      };

      // Check if user exists in our system
      const { user, isNewUser } = await this.authenticateWithBackend(googleUser, idToken);

      return { user: googleUser, isNewUser };
    } catch (error) {
      console.error('Google Sign-In failed:', error);
      throw new Error('Google Sign-In failed. Please try again.');
    }
  }

  /**
   * Sign out from Google
   */
  async signOut(): Promise<void> {
    if (!this.isInitialized) return;

    try {
      await this.auth2.signOut();
      // Also sign out from our backend
      apiService.logout();
      console.log('✅ Signed out successfully');
    } catch (error) {
      console.error('Sign out failed:', error);
    }
  }

  /**
   * Get current signed-in user
   */
  getCurrentUser(): GoogleUser | null {
    if (!this.isInitialized || !this.auth2.isSignedIn.get()) {
      return null;
    }

    const user = this.auth2.currentUser.get();
    const profile = user.getBasicProfile();

    return {
      id: profile.getId(),
      email: profile.getEmail(),
      name: profile.getName(),
      given_name: profile.getGivenName(),
      family_name: profile.getFamilyName(),
      picture: profile.getImageUrl(),
      verified_email: true
    };
  }

  /**
   * Check if user is signed in
   */
  isSignedIn(): boolean {
    return this.isInitialized && this.auth2?.isSignedIn.get();
  }

  /**
   * Authenticate with our backend using Google credentials
   */
  private async authenticateWithBackend(googleUser: GoogleUser, idToken: string): Promise<{ user: any; isNewUser: boolean }> {
    try {
      // Try to login with existing Google account
      const loginResponse = await apiService.googleLogin({
        id_token: idToken,
        google_id: googleUser.id,
        email: googleUser.email
      });

      return { user: loginResponse.user, isNewUser: false };
    } catch (error: any) {
      // If user doesn't exist, create new account
      if (error.message.includes('User not found') || error.message.includes('404')) {
        return this.createNewUserAccount(googleUser, idToken);
      }
      throw error;
    }
  }

  /**
   * Create new user account with Google credentials
   */
  private async createNewUserAccount(googleUser: GoogleUser, idToken: string): Promise<{ user: any; isNewUser: boolean }> {
    const registrationData = {
      email: googleUser.email,
      name: googleUser.name,
      firstName: googleUser.given_name,
      lastName: googleUser.family_name,
      profileImage: googleUser.picture,
      provider: 'google',
      providerId: googleUser.id,
      location: {
        country: 'Zimbabwe', // Default for RoadPulse
        city: undefined
      }
    };

    try {
      const registerResponse = await apiService.googleRegister({
        id_token: idToken,
        user_data: registrationData
      });

      return { user: registerResponse.user, isNewUser: true };
    } catch (error) {
      console.error('Failed to create user account:', error);
      throw new Error('Failed to create account. Please try again.');
    }
  }

  /**
   * Listen for auth state changes
   */
  onAuthStateChanged(callback: (isSignedIn: boolean, user: GoogleUser | null) => void): () => void {
    if (!this.isInitialized) {
      // Initialize first, then set up listener
      this.initialize().then(() => {
        if (this.isInitialized && this.auth2) {
          this.auth2.isSignedIn.listen((isSignedIn: boolean) => {
            const user = isSignedIn ? this.getCurrentUser() : null;
            callback(isSignedIn, user);
          });
        }
      }).catch((error) => {
        console.warn('Google Auth initialization failed, skipping auth state listener:', error);
        // Call callback with signed out state
        callback(false, null);
      });
      return () => {}; // Return empty cleanup function
    }

    this.auth2.isSignedIn.listen((isSignedIn: boolean) => {
      const user = isSignedIn ? this.getCurrentUser() : null;
      callback(isSignedIn, user);
    });

    // Return cleanup function
    return () => {
      // Google API doesn't provide a way to remove specific listeners
      // This is handled automatically when the component unmounts
    };
  }

  /**
   * Get service status
   */
  getStatus(): {
    isInitialized: boolean;
    isSignedIn: boolean;
    hasClientId: boolean;
  } {
    return {
      isInitialized: this.isInitialized,
      isSignedIn: this.isSignedIn(),
      hasClientId: !!GOOGLE_CONFIG.clientId
    };
  }
}

// Extend window interface for Google API
declare global {
  interface Window {
    gapi: any;
  }
}

// Export singleton instance
export const googleAuthService = new GoogleAuthService();

// Export for testing
export { GoogleAuthService };
